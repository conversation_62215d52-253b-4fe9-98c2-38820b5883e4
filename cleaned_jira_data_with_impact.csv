Issue Type,Key,Priority,Summary,Creator,Created,Date of First Response,Redeclared,Description,cleaned_description,sentiment,Resolution Time (Days),days_old,temporal_decay,sentiment_impact,decayed_ticket_count,ticket_impact,priority_impact,Issue_Type_impact,urgency_score
Incident,OEKB-6887,Medium,M12 SCoRE Std. 15 - 15022 OUT could not be generated due to long COAF,Stefan RIBISCH,2025-05-16 10:15:00,,NO,"Yesterday I created some events with seev.031, as they were come from Clearstream (attached). As in 20022 standard it is allowed to have 35 characters in reference fields I didn't take care that for COAF I took more than 16 characters which is the limit on 15022 standard.Now the generation of all 15022 OUT failed in MegaBroker due to following error:I understand the reason for this error, but as this could happen everytime if the custodian sends 20022 and our clients receive 15022: What is your solution for this topic? Has this been discussed in rounds concerning the new ISO20022 standards what happens if references (or maybe also other fields) have more characters space in 20022 than in 15022? What should happen then?At the moment, the clients wouldn't receive any notification for events which were created by ISO20022 with a COAF greater than 16 characters.Please advise how this should work and what your thoughts are about this situation.Thanks,stefan",yesterday created event seev come clearstream attached standard allowed character reference field didnt take care coaf took character limit standardnow generation failed megabroker due following errori understand reason error could happen everytime custodian sends client receive solution topic discussed round concerning new iso standard happens reference maybe also field character space happen thenat moment client wouldnt receive notification event created iso coaf greater charactersplease advise work thought situationthanksstefan,-0.11361059918999672,0.0,13,0.8051983240180706,0.2784026497974992,18.**************,0.2,0.1,0.15,59.26%
Incident,OEKB-6880,Medium,M12 SCoRE Std. 01 - DRAW-event wrong calculation,Stefan RIBISCH,2025-05-14 14:30:00,,NO,"Event DRAW0000005242I took an example-SWIFT from production and just changed some figures and dates and created a DRAW-event in QAS - this worked so far.Unfortunately when calculating entitlements and sending REPE-SWIFTs I noticed that MegaCor would debit 100% of the shares which is not correct.In our opinion the system should calculate the redemption rate and calculate it for every sec account separately instead of taking 100% for every client.Further: In View-Screen there is a redemption rate in SEC OUT part visible, in Create- and Update-Screen this field is not visible (see screenshots).Attached you find the MT564-in and two examples of MT564 REPE and seev.035 REPE OUT.Please check and explain how the system should work and what is the correct behaviour in these cases and why the redemption rate is only visible in VIEW-Screen.Thanks,stefan",event drawi took exampleswift production changed figure date created drawevent qas worked farunfortunately calculating entitlement sending repeswifts noticed megacor would debit share correctin opinion system calculate redemption rate calculate every sec account separately instead taking every clientfurther viewscreen redemption rate sec part visible create updatescreen field visible see screenshotsattached find mtin two example mt repe seev repe outplease check explain system work correct behaviour case redemption rate visible viewscreenthanksstefan,0.016225842759013176,0.0,15,0.****************,0.****************,18.**************,0.2,0.1,0.15,54.39%
Incident,OEKB-6876,Cosmetic,M12 QAS: Exercice instead of Exercise,Stefan RIBISCH,2025-05-12 13:24:00,,NO,"We had this topic already - please change the wording of Exercice to Exercise as it is seen by clients in ASOC.I found it in the EXWA MAND event now. In EXWA CHOS it is called “Exercise” and in EXWA VOLU it is called “EXER”.Please make sure that it is correct on all places.Thanks,stefan",topic already please change wording exercice exercise seen client asoci found exwa mand event exwa chos called exercise exwa volu called exerplease make sure correct placesthanksstefan,0.019710607826709747,0.0,17,0.7532686564546568,0.24507234804332256,18.**************,0.2,0.1,0.15,54.26%
Incident,OEKB-6868,Critical,M12 SCoRE: Std. 8 NTS - REPE to 3i not generated,Stefan RIBISCH,2025-05-07 10:09:00,2025-05-07 14:01:00,NO,"For three events, the entitlements have been calculated correctly after full load and REPE has been generated and sent: INTR0000004353, INTR0000004358, DVCA0000005239Unfortunately we cannot open the REPE to 3i.In MegaBroker, the three FlowINs came in with ERROR=TRUE with the attached error message and no Flow Out is found.Please check why these REPEs now do not come to 3i.Thanks,stefan",three event entitlement calculated correctly full load repe generated sent intr intr dvcaunfortunately open repe iin megabroker three flowins came errortrue attached error message flow foundplease check repes come ithanksstefan,0.007558327168226242,0.0,22,0.****************,0.24811041820794344,18.**************,0.2,0.15,0.15,62.22%
Incident,OEKB-6867,Major,M12 SCore: Std. 15 - BPUT: missing tag in outgoing seev.031,Stefan RIBISCH,2025-05-07 09:27:00,,NO,"Event BPUT0000003496This event has the following election period (without begin date) in both options in MegaCor.A NEWM and a RMDR have been sent out.The result in MT564: „:69E::PWAL//UKWN/20250502“ which looks good.The result in seev.031 is unfortunately the followint for both options in both messages the same (see attachment).Due to that, the following error message is received in SWIFT-testtool (see attachment).Please check and correct that.Thanks,stefan",event bputthis event following election period without begin date option megacora newm rmdr sent outthe result mt epwalukwn look goodthe result seev unfortunately followint option message see attachmentdue following error message received swifttesttool see attachmentplease check correct thatthanksstefan,-0.043600019067525864,0.0,22,0.****************,0.*****************,18.**************,0.2,0.13,0.15,61.14%
Incident,OEKB-6864,Major,M12 SCoRE: Std. 4 - Rounding of fees,Stefan RIBISCH,2025-05-06 13:34:00,2025-05-07 10:44:00,NO,"Event SHPR0000004884Fees per amount in MegaCor: 0,033333Example Client Sec Account 222100: 1.481 sharesMegaCor calculated fees of 49,36 but when we checked it turned out that it was round down by error: 1.481*0,033333=49,366173 which should lead to 49,37 feesAttached you find outgoing SWIFTs which show this amound and also the entitlement and the payment of this client.Please check that.Thanks, stefan",event shprfees per amount megacor example client sec account sharesmegacor calculated fee checked turned round error lead feesattached find outgoing swift show amound also entitlement payment clientplease check thatthanks stefan,-0.021035097539424896,0.0,23,0.****************,0.****************,18.**************,0.2,0.13,0.15,60.29%
Incident,OEKB-6861,Medium,"M12 QAS - TEND0000005231 - seev.033 IN - FlowOut in status ""WaitingAck""",Bertram Schon,2025-05-05 08:47:00,2025-05-05 10:40:00,NO,"TEND0000005231When trying to import a client instruction (seev.033 attached), in MegaBroker, the FlowIn is in status “ValidFlow”, but the FlowOut is still in status “WaitingAck” and did not reach MegaCor.Could you please check what is wrong here?Thanks, Bert",tendwhen trying import client instruction seev attached megabroker flowin status validflow flowout still status waitingack reach megacorcould please check wrong herethanks bert,-0.024046044796705246,0.0,24,0.****************,0.****************,10.***************,0.*****************,0.1,0.15,42.69%
Incident,OEKB-6853,Major,M12 SCoRE: Std. 15 - EXWA CASH Event - seev.035 Cash price is missing,Stefan RIBISCH,2025-04-30 09:53:00,2025-04-30 09:58:00,NO,"test with EXWA0000005046Hello,the seev.035 message does not include the cash price received per product.The MT564REPE provides this information.Please check\!Thank you and BR, Dalibor",test exwahellothe seev message include cash price received per productthe mtrepe provides informationplease checkthank br dalibor,0.03304808773100376,0.0,29,0.6167242143691608,0.24173797806724906,18.**************,0.2,0.13,0.15,58.26%
Incident,OEKB-6852,Major,M12 SCoRE: Std. 15 - PRED Event seev.036 Incorrect redemption rate and missing pool factor,Stefan RIBISCH,2025-04-30 09:51:00,2025-04-30 09:59:00,NO,"test with PRED0000005071Hello,for the PRED event mentioned above, the redemption rate on the seev.036 message is incorrect. It is not identical to the redemption rate on the MT566NEWM message.However, the amount is calculated correctly.The pool factor (_NWFC_ and PRFC) are also missing.Please check\!Thank you and BR, Dalibor",test predhellofor pred event mentioned redemption rate seev message incorrect identical redemption rate mtnewm messagehowever amount calculated correctlythe pool factor nwfc prfc also missingplease checkthank br dalibor,0.04062899947166443,0.0,29,0.6167242143691608,0.2398427501320839,18.**************,0.2,0.13,0.15,57.98%
Incident,OEKB-6851,Major,M12 SCoRE: Std. 15 - PRED Event seev.035 Incorrect redemption rate and missing pool factor,Stefan RIBISCH,2025-04-30 09:45:00,2025-04-30 09:59:00,NO,"test with PRED0000005071Hello,for the PRED event mentioned above, the redemption rate on the seev.035 message is incorrect. It is not identical to the redemption rate on the MT564REPE message.However, the entitlements are calculated correctly.The pool factor (_NWFC_ and PRFC) are also missing.Please check\!Thank you and BR, Dalibor",test predhellofor pred event mentioned redemption rate seev message incorrect identical redemption rate mtrepe messagehowever entitlement calculated correctlythe pool factor nwfc prfc also missingplease checkthank br dalibor,0.051295289769768715,0.0,29,0.6167242143691608,0.23717617755755782,18.**************,0.2,0.13,0.15,57.58%
Incident,OEKB-6850,Major,M12 SCoRE - Standard 15: PRED Event seev.031 Incorrect redemption rate,Stefan RIBISCH,2025-04-30 09:37:00,2025-04-30 09:48:00,NO,"test with PRED0000005071Hello,for the PRED event mentioned above, the redemption rate on the seev.031 and seev.031 REPL message is incorrect. It is not identical to the one on the MT564NEWM and MT564REPL message.Please check\!Thank you and BR, Dalibor",test predhellofor pred event mentioned redemption rate seev seev repl message incorrect identical one mtnewm mtrepl messageplease checkthank br dalibor,0.06224016100168228,0.0,29,0.6167242143691608,0.23443995974957943,18.**************,0.2,0.13,0.15,57.17%
Incident,OEKB-6831,Major,M12 QAS: Update REDM Price,Stefan RIBISCH,2025-04-22 15:06:00,,NO,"Test with REDM0000000336Hello,I would like to change the redemption price for the Event I have mentioned above.When I try this, there are *three fields* where you can enter the redemption rate, which is not the case in M12 production.I have tried to change only the 'Redemption Price' but the update is not being saved.Please check\!Thank you and BR, Dali",test redmhelloi would like change redemption price event mentioned abovewhen try three field enter redemption rate case productioni tried change redemption price update savedplease checkthank br dali,0.08080961555242538,0.0,37,0.5397405776236126,0.22979759611189365,18.**************,0.2,0.13,0.15,56.47%
Incident,OEKB-6828,Major,"M12 QAS: PROX - seev.004 with ""Vote For All Agenda Resolutions"" rejected",Stefan RIBISCH,2025-04-17 13:40:00,2025-04-17 14:33:00,YES,"Event PROX0000005001Due to a case in production we tested this first in QAS and unfortunately a seev.004 instruction from the client with voting for all agenda resolution was immediately rejected by MegaCor due to the attached error.You find the seev.004 also attached.Please check and fix this as this makes it much easier if an event has many resolution points like this event (23\!).Thanks,stefan",event proxdue case production tested first qas unfortunately seev instruction client voting agenda resolution immediately rejected megacor due attached erroryou find seev also attachedplease check fix make much easier event many resolution point like event thanksstefan,-0.23253720998764038,0.0,42,0.4965853037914095,0.3081343024969101,18.**************,0.2,0.13,0.15,68.22%
Incident,OEKB-6825,Major,M12 QAS: PROX - outgoing seev.004 without content,Stefan RIBISCH,2025-04-17 10:23:00,,NO,"Today the generation failed for a seev.04 OUT for event PROX0000005001.According to MegaBroker Generation failed due to following error:*PALM-15037: The content Austria of the value CtryOfIncorprtn\[Document.MtgInstr.Instr.AcctDtls.RghtsHldr.RghtsHldr_Choice_0.LglPrsn.CtryOfIncorprtn.CtryOfIncorprtn] is not valid. It should verify this pattern \[A-Z]\{2,2}. The length of this value should be between 0 and 2147483647.*Anyway I created the PV CI instruction with attached content.If the system requires a 2-digit-country-code in both fields it should be mandatory when creating. Better would be to offer a list of possible values. But afterwards an error in message generation when it cannot be edited anymore is no good solution.As this will not be done very often this could lead to problems when it is not clear that a code is necessary in both fields.Please check this topic - thanks\!stefan",today generation failed seev event proxaccording megabroker generation failed due following errorpalm content austria value ctryofincorprtndocumentmtginstrinstracctdtlsrghtshldrrghtshldrchoicelglprsnctryofincorprtnctryofincorprtn valid verify pattern az length value anyway created pv ci instruction attached contentif system requires digitcountrycode field mandatory creating better would offer list possible value afterwards error message generation edited anymore good solutionas done often could lead problem clear code necessary fieldsplease check topic thanksstefan,-0.1782114952802658,0.0,42,0.4965853037914095,0.29455287382006645,18.**************,0.2,0.13,0.15,66.18%
Incident,OEKB-6823,Critical,M12 QAS: No payment confirmation can be sent,Stefan RIBISCH,2025-04-16 15:16:00,2025-04-16 15:39:00,NO,"We have some examples today where we cannot send payment confirmations although the client payments are in status Created: DVCA0000004991, LIQU0000004992, MCAL0000004993, DVCA0000004994.Please check that as this worked already fine hundreds of times.Thanks,stefan",example today send payment confirmation although client payment status created dvca liqu mcal dvcaplease check worked already fine hundred timesthanksstefan,0.1537926234304905,0.0,43,0.4883774706661871,0.21155184414237738,18.**************,0.2,0.15,0.15,56.73%
Incident,OEKB-6822,Major,M12 QAS: Tax amount where no tax should be,Stefan RIBISCH,2025-04-16 14:53:00,2025-04-17 09:24:00,NO,"Event SHPR0000004884This share premium event has a gross and a fee amount (and of course a net amount) but definitive no tax amount.In the client entitlements (and so also in outgoing seev.035/MT564 REPE) there was no tax but unfortunately in the client payments and so also in outgoing seev.036/MT566 the system shows a tax amount in the same amount as the fees which is wrong.Please check and correct that for all event types in MegaCor.Thanks,stefan",event shprthis share premium event gross fee amount course net amount definitive tax amountin client entitlement also outgoing seevmt repe tax unfortunately client payment also outgoing seevmt system show tax amount amount fee wrongplease check correct event type megacorthanksstefan,-0.008958660066127777,0.0,43,0.4883774706661871,0.25223966501653194,18.**************,0.2,0.13,0.15,59.84%
Incident,OEKB-6821,Major,M12 QAS: No fees on MT564 NEWM and MT564 REPE,Stefan RIBISCH,2025-04-16 13:53:00,2025-04-17 09:25:00,NO,"Event SHPR0000004884Also for MT564 NEWM and MT564 REPE, the fees per share are not included.Please check that.Thanks,stefan",event shpralso mt newm mt repe fee per share includedplease check thatthanksstefan,0.012721868231892586,0.0,43,0.4883774706661871,0.24681953294202685,18.**************,0.2,0.13,0.15,59.02%
Incident,OEKB-6820,Major,M12 SCoRE: Std. 15 - Fees missing in seev.031,Stefan RIBISCH,2025-04-16 13:44:00,2025-04-24 18:04:00,NO,"Event SHPR0000004884although in this event fees are filled in (see attachment)they are not included in the outgoing seev.031 (you find it also attached) and also not in the two seev.035 (two seev.035 because of breakdown instruction - you find both attached).Further - only in one of these two seev035 there is a total charge amount to find, in the other one, this line is missing.Please check why the fees - at least partly - are missing in the SWIFTsstefan",event shpralthough event fee filled see attachmentthey included outgoing seev find also attached also two seev two seev breakdown instruction find attachedfurther one two seev total charge amount find one line missingplease check fee least partly missing swiftsstefan,-0.08216740936040878,8.0,43,0.4883774706661871,0.2705418523401022,18.**************,0.2,0.13,0.15,62.58%
Incident,OEKB-6815,Major,M12 SCoRE: Std. 13 - seev.035 without content,Stefan RIBISCH,2025-04-15 13:41:00,2025-04-15 15:22:00,NO,"Event EXWA0000004975After creating a reversal request the notification seev.035 could be generated, but Generation Failed in MegaBroker due to following error.Please check what the problem is with this notification.Thanks,stefan",event exwaafter creating reversal request notification seev could generated generation failed megabroker due following errorplease check problem notificationthanksstefan,-0.8946690373122692,0.0,44,0.4803053010897994,0.4736672593280673,18.**************,0.2,0.13,0.15,93.05%
Incident,OEKB-6813,Major,M12 SCoRE: Std. 15 - INTR0000003895 - seev.036 OUT,Stefan RIBISCH,2025-04-15 08:02:00,2025-04-15 16:06:00,YES,"INTR0000003895Hello,we have noticed that the following information are not included in the seev.036 messages:interest calculation methodmaturity datecoupon dateinterest periodNumber of Days Accruedinterest ratevalue datePlease check\!Thank you and BR, Dalibor",intrhellowe noticed following information included seev messagesinterest calculation methodmaturity datecoupon dateinterest periodnumber day accruedinterest ratevalue dateplease checkthank br dalibor,-0.04290050454437733,0.0,44,0.4803053010897994,0.26072512613609433,18.**************,0.2,0.13,0.15,61.11%
Incident,OEKB-6811,Major,M12 SCoRE - EXRI0000004930 - Reversal Request - MT564/seev.035 bugs,Bertram Schon,2025-04-14 15:08:00,2025-05-05 13:30:00,NO,"EXRI0000004930Reversal Request (only) for BUYA Option was entered manually and validated.When checking MT564 REPE / seev.035 (attached), I detected the following bugs:* MT564: Function of the message should be REPE not ADDB (already handled in OEKB-6805)* MT564 and seev.035: all options are populated in the message, but only one (BUYA) is subject to reversal\! Is there an option to only populate the relevant option to be reversed?* seev.035: EXER and SLLE options contain the same ""Cash Movement Details"" which are incorrect, as those belong to the BUYA entitlement =><CshMvmntDtls><CdtDbtInd>CRDT</CdtDbtInd><CshAcctId><Prtry>CATEURBKAUATWWXXXMAINACC</Prtry></CshAcctId><AmtDtls><GrssCshAmt Ccy=""EUR"">2664</GrssCshAmt><NetCshAmt Ccy=""EUR"">2664</NetCshAmt><EntitldAmt Ccy=""EUR"">2664</EntitldAmt></AmtDtls><DtDtls><PmtDt><DtCd><Cd>UKWN</Cd></DtCd></PmtDt><ValDt><Dt>2025-04-14</Dt></ValDt></DtDtls></CshMvmntDtls>Could you please check?Thanks, Bert",exrireversal request buya option entered manually validatedwhen checking mt repe seev attached detected following bug mt function message repe addb already handled oekb mt seev option populated message one buya subject reversal option populate relevant option reversed seev exer slle option contain cash movement detail incorrect belong buya entitlement cshmvmntdtlscdtdbtindcrdtcdtdbtindcshacctidprtrycateurbkauatwwxxxmainaccprtrycshacctidamtdtlsgrsscshamt ccyeurgrsscshamtnetcshamt ccyeurnetcshamtentitldamt ccyeurentitldamtamtdtlsdtdtlspmtdtdtcdcdukwncddtcdpmtdtvaldtdtdtvaldtdtdtlscshmvmntdtlscould please checkthanks bert,-0.*****************,20.0,45,0.****************,0.*****************,10.***************,0.*****************,0.13,0.15,47.81%
Incident,OEKB-6810,Medium,M12 ASOC QAS: Number-Codes instead of error messages,Stefan RIBISCH,2025-04-14 14:36:00,2025-04-14 17:22:00,NO,"Event EXOF0000004880 (Ref. Date 23.06.2025), Account 222100For this event for this account I created two instructions in MegaCor which were immediately visible in ASOC (see attachment).After clicking on the Details-icon of the rejected instruction you only see number-codes instead of the real error message. In addition: Everytime I open this screen, this code could be different - so it seems this is a random code.Attached you see the error messages in MegaCor.I checked already with OEKB IT and got following feedback:_“The following messages are delivered in the instruction search of Megacor:__2025-04-08 16:06:22,472 \[https-jsse-nio-8175-exec-10] INFO  at.oekb.casa.model.webservices.RestServiceProvider - Json-Object at.oekb.casa.swcodegen.megacor.model.InstructionSearchParams;__\{""pageSize"":1,""client"":\[\\\{""value"":""222100""}__],""pageNumber"":1,""instrAppReference"":\[\\\{""value"":""ISCI000000001405""}],""forValidation"":false}__================__2025-04-08 16:06:22,530 \[https-jsse-nio-8175-exec-10] INFO  at.oekb.casa.model.webservices.RestServiceProvider - Json-Object java.util.ArrayList;__\[\{""creationDate"":""2025-04-03 00:00:00.000+0000"",""source"":""Manual"",""status"":""Rejected"",""entitledIsin"":""IT0005218380"",""client"":""222100"",""appReference"":""ISCI000000001405"",""quantityType"":""UNIT"",""updateDate"":""2025-04-03 00:00:00.000+0000"",""clientReference"":""b03042501"",""instructedQuantity"":300,""clientDeadlineTime"":""2025-06-20 08:00:00.000+0000"",""clientDeadlineTimeZone"":""+02:00"",""mainReference"":""EXOF0000004880"",""updatorUserID"":""scb"",""creatorUserID"":""scb"",""statusDetails"":""Inst_Rejected"",""optionType"":""001 - Secu Option"",""businessError"":\[__\{""code"":""5540""}__,\{""code"":""5539""}],""securityAccount"":""222100"",""code"":""5541""}]“_Please check this topic as our clients need correct error messages.Thanks,stefan",event exof ref date account event account created two instruction megacor immediately visible asoc see attachmentafter clicking detailsicon rejected instruction see numbercodes instead real error message addition everytime open screen code could different seems random codeattached see error message megacori checked already oekb got following feedbackthe following message delivered instruction search megacor info atoekbcasamodelwebservicesrestserviceprovider jsonobject atoekbcasaswcodegenmegacormodelinstructionsearchparamspagesizeclientvaluepagenumberinstrappreferencevalueisciforvalidationfalse info atoekbcasamodelwebservicesrestserviceprovider jsonobject javautilarraylistcreationdate sourcemanualstatusrejectedentitledisinitclientappreferenceisciquantitytypeunitupdatedate clientreferencebinstructedquantityclientdeadlinetime clientdeadlinetimezonemainreferenceexofupdatoruseridscbcreatoruseridscbstatusdetailsinstrejectedoptiontype secu optionbusinesserrorcodecodesecurityaccountcodeplease check topic client need correct error messagesthanksstefan,-0.****************,0.0,45,0.****************,0.****************,18.**************,0.2,0.1,0.15,58.49%
Incident,OEKB-6809,Major,M12 ASOC QAS: Internal Error when click on 'Search Message',Stefan RIBISCH,2025-04-14 14:20:00,2025-04-14 17:18:00,NO,"Event EXOF0000004880after filling in an account number 222100 and the Main Reference EXOF0000004880 and tclicking on ‘Search Messages’ in ASOC, the attached error message occurs.I checked already with OEKB IT and got following feedback from them:_“The following search is sent to Megacor:__Json-Object at.oekb.casa.swcodegen.megacor.model.NotificationSearchParams; \{""pageSize"":100000,""client"":\[__\{""value"":""222100""}__],""pageNumber"":1,""creationDateFrom"":""2025-04-01 14:12:00.000+0000"",""creationDateTo"":""2025-04-08 14:12:00.000+0000"",""mainReference"":\[\\\{""value"":""EXOF0000004880""}]}__But no answer is received back.__Error message from Logs:__at.oekb.casa.swcodegen.megacor.invoker.ApiException: com.fasterxml.jackson.databind.exc.MismatchedInputException: No content to map due to end-of-input__Please clarify with Vermeg.”_Please check that\!Thanks,stefan",event exofafter filling account number main reference exof tclicking search message asoc attached error message occursi checked already oekb got following feedback themthe following search sent megacorjsonobject atoekbcasaswcodegenmegacormodelnotificationsearchparams pagesizeclientvaluepagenumbercreationdatefrom creationdateto mainreferencevalueexofbut answer received backerror message logsatoekbcasaswcodegenmegacorinvokerapiexception comfasterxmljacksondatabindexcmismatchedinputexception content map due endofinputplease clarify vermegplease check thatthanksstefan,-0.*****************,0.0,45,0.****************,0.*****************,18.**************,0.2,0.13,0.15,62.26%
Incident,OEKB-6808,Major,M12 SCoRE: Std. 15 - no seev.035 REVR for all entitlements/payments,Stefan RIBISCH,2025-04-14 13:43:00,2025-04-15 16:17:00,NO,"Event DVCA0000004883For client 222100 three entitlements exist (1.001, 480 and 16.000 shares) due to breakdown instructions (see attachment).After creating a reversal request we noticed that only for one of these three entitlements (for the 1.001 shares - the one without breakdown instruction) a seev.035 REVR has been generated - for the others not.Please check that as this seems not correct in our opinion.Thanks,stefan",event dvcafor client three entitlement exist share due breakdown instruction see attachmentafter creating reversal request noticed one three entitlement share one without breakdown instruction seev revr generated others notplease check seems correct opinionthanksstefan,-0.04958859458565712,1.0,45,0.****************,0.2623971486464143,18.**************,0.2,0.13,0.15,61.36%
Incident,OEKB-6807,Major,M12 SCoRE: Std. 15 - ADTX missing on outgoing seev.035,Stefan RIBISCH,2025-04-14 11:43:00,2025-04-16 11:14:00,NO,"Event DVCA0000004883on all outgoing seev.035 the outgoing comment was missing. On seev.031 it was included. You find all messages attached.We noticed that also for these holdings with breakdown instructions (one for 16.000 shs and one for 480 shs), the seev.035 do not have the standard text as for ISO 15022 messages - see attached an examples.Please check that the outgoing comments and the texts for breakdown instructions are overtaken into the outgoing ISO20022 messages for all events.Thanks,stefan",event dvcaon outgoing seev outgoing comment missing seev included find message attachedwe noticed also holding breakdown instruction one shs one shs seev standard text iso message see attached examplesplease check outgoing comment text breakdown instruction overtaken outgoing iso message eventsthanksstefan,-0.1072955671697855,1.0,45,0.****************,0.2768238917924464,18.**************,0.2,0.13,0.15,63.52%
Incident,OEKB-6805,Major,M12 SCoRE - RHDI0000004759 - MT564 Reversal Notification - :23G:: should be REPE (not ADDB),Bertram Schon,2025-04-11 15:54:00,2025-04-28 09:54:00,NO,"RHDI0000004759When checking MT564 Reversal Notification (attached), we noticed that the Message Function (:23G::) is ADDB.This doesn't comply with the SMPG Global Market Practice guide (see screenshot).Could you please check and amend the Message Function to REPE?Thanks, Bert",rhdiwhen checking mt reversal notification attached noticed message function g addbthis doesnt comply smpg global market practice guide see screenshotcould please check amend message function repethanks bert,0.009781019762158394,16.0,48,0.44932896411722156,0.2475547450594604,10.***************,0.*****************,0.13,0.15,45.92%
Incident,OEKB-6802,Major,M12 SCoRE - EXRI0000004930 - seev.036 OUT BUYA/SLLE Option - Mapping errors,Bertram Schon,2025-04-10 11:16:00,,NO,"EXRI0000004930When checking seev.036 for EXRI BUYA/SLLE option (attached), I detected the following mapping issues:* Broker Price is missing(this bug also relates to MT566 OUT, see OEKB-6801)* Trade Date/Time is missing(see, in comparison, MT566 OUT::13A::CAON//002:22F::CAOP//SLLE:98C::TRAD//20250409104700)* RateDtls (SLLE Option) should be skipped:<RateDtls><AddtlQtyForExstgScties><QtyToQty><Qty1>1</Qty1><Qty2>1</Qty2></QtyToQty></AddtlQtyForExstgScties></RateDtls>In addition, there are the same mapping issues as decribed in OEKB-6797.Could you please check?Thanks, Bert",exriwhen checking seev exri buyaslle option attached detected following mapping issue broker price missingthis bug also relates mt see oekb trade datetime missingsee comparison mt outacaonfcaopsllectrad ratedtls slle option skippedratedtlsaddtlqtyforexstgsctiesqtytoqtyqtyqtyqtyqtyqtytoqtyaddtlqtyforexstgsctiesratedtlsin addition mapping issue decribed oekbcould please checkthanks bert,-0.14290795661509037,0.0,49,0.****************,0.2857269891537726,10.***************,0.*****************,0.13,0.15,51.65%
Incident,OEKB-6801,Medium,M12 QAS - EXRI0000004930 - MT566 for BUYA/SLLE doesn't include Broker Price,Bertram Schon,2025-04-10 10:17:00,2025-04-28 09:57:00,NO,"EXRI0000004930When checking MT566 OUT for EXRI event BUYA/SLLE option (attached), I noticed that the Broker Price is missing.In comparison, see MT566 OUT generated in M10 =>:13A::CAON//003:22F::CAOP//*BUYA*:98C::TRAD//20230626113651:16R:CASHMOVE:22H::CRDB//DEBT:97A::CASH//CATEURRZBAATWWXXXOEKB:19B::PSTA//EUR39,68:19B::NETT//EUR39,68:98A::POST//20230628:98A::VALU//20230628*:90B::PRPP//ACTU/EUR6,61333*:13A::CAON//002:22F::CAOP//*SLLE*:98C::TRAD//20230623113004:16R:SECMOVE:22H::CRDB//DEBT:35B:ISIN AT0000A35PJ0BR A/LENZING AKT O.N.:36B::PSTA//UNIT/64147,:98A::POST//20230627:16S:SECMOVE:16R:CASHMOVE:22H::CRDB//CRED:97A::CASH//CDEEURFMBKDEMMGRPCBDCLIENTFR:19B::PSTA//EUR486542,17:19B::NETT//EUR486542,17:98A::POST//20230627:98A::VALU//20230627*:90B::OFFR//ACTU/EUR7,5848*Could you please check and make sure that the mapping will be amended also in PROD?Thanks, Bert",exriwhen checking mt exri event buyaslle option attached noticed broker price missingin comparison see mt generated acaonfcaopbuyactradrcashmovehcrdbdebtacashcateurrzbaatwwxxxoekbbpstaeurbnetteurapostavalubprppactueuracaonfcaopsllectradrsecmovehcrdbdebtbisin atapjbr alenzing akt onbpstaunitapostssecmovercashmovehcrdbcredacashcdeeurfmbkdemmgrpcbdclientfrbpstaeurbnetteurapostavaluboffractueurcould please check make sure mapping amended also prodthanks bert,0.0007764808833599091,17.0,49,0.****************,0.*****************,10.***************,0.*****************,0.1,0.15,41.76%
Incident,OEKB-6797,Major,M12 SCoRE - EXRI0000004930 - seev.036 OUT - Mapping errors,Bertram Schon,2025-04-09 14:59:00,2025-04-09 15:06:00,NO,"EXRI0000004930When checking seev.036 OUT (attached), I noticed the following:* linkage to the client instruction reference is missing(see, in comparison, MT566 OUT::13A::LINK//565:20C::RELA//eg0904202501:16S:LINK)* cash account for both main payment and fee debit missing(see, in comparision, MT566 OUT::97A::CASH//CATEURGIBAATWGXXXPROD:97A::CASH//***********)* Main payment: subscription price is populated as ""Gross Dividend Rate"":<GrssDvddRate><Amt Ccy=""EUR"">3.17</Amt></GrssDvddRate>I guess, ""Generic Cash Price Paid Per Product"" must be used here?* Fee payment: subscription price is populated also in this sequence (see screenshot) and should be skipped, no information available about fee debit, see in comparison MT566 OUT::19B::CHAR//EUR10,Could you please check?Thanks, Bert",exriwhen checking seev attached noticed following linkage client instruction reference missingsee comparison mt outalinkcrelaegslink cash account main payment fee debit missingsee comparision mt outacashcateurgibaatwgxxxprodacash main payment subscription price populated gross dividend rategrssdvddrateamt ccyeuramtgrssdvddratei guess generic cash price paid per product must used fee payment subscription price populated also sequence see screenshot skipped information available fee debit see comparison mt outbchareurcould please checkthanks bert,-0.022080954164266586,0.0,50,0.****************,0.*****************,10.***************,0.*****************,0.13,0.15,47.12%
Incident,OEKB-6795,Medium,M12 SCoRE - EXRI0000004930 - seev.034 OUT - LINK to instruction received missing,Bertram Schon,2025-04-09 11:20:00,2025-04-09 11:34:00,NO,"EXRI0000004930When checking seev.034 OUT, I noticed that there is no LINK to the instruction received from the client, like it is available in MT567 OUT =>:16R:LINK:13A::LINK//565:20C::RELA//eg0904202501Could you please check how to populate the related instruction reference also in seev.034 OUT?Thanks, Bert",exriwhen checking seev noticed link instruction received client like available mt rlinkalinkcrelaegcould please check populate related instruction reference also seev outthanks bert,0.04187969211488962,0.0,50,0.****************,0.2395300769712776,10.***************,0.*****************,0.1,0.15,40.22%
Incident,OEKB-6794,Major,M12 SCoRE - EXRI0000004930 - seev.034 OUT - <SfkpgAcct> must include Prefix 'OCSD',Bertram Schon,2025-04-09 11:07:00,2025-04-09 11:12:00,NO,"EXRI0000004930When checking seev.034 OUT (attached), I noticed that <SfkpgAcct> is populated without the Prefix 'OCSD'.Please make sure that <SfkpgAcct> includes 'OCSD' in every outgoing message.Thanks, Bert",exriwhen checking seev attached noticed sfkpgacct populated without prefix ocsdplease make sure sfkpgacct includes ocsd every outgoing messagethanks bert,0.019865291193127632,0.0,50,0.****************,0.2450336772017181,10.***************,0.*****************,0.13,0.15,45.54%
Incident,OEKB-6792,Major,M12 QAS - EXRI0000004930 - BUYA/SLLE ONGO not saved,Bertram Schon,2025-04-08 14:55:00,2025-04-08 16:32:00,NO,"EXRI0000004930Pay Date of Cash Movement in SLLE and BUYA Option was set to ONGO (screenshot).However, after validating the update, the flag is still set to false (screenshot 2).Could you please check and also make sure that this bug is fixed also in PROD?\!BR Bert",exripay date cash movement slle buya option set ongo screenshothowever validating update flag still set false screenshot could please check also make sure bug fixed also prodbr bert,-0.1776229552924633,0.0,51,0.4274149319487267,0.2944057388231158,10.***************,0.*****************,0.13,0.15,52.95%
Incident,OEKB-6791,Major,M12 SCoRE - EXRI0000004930 - seev.031 OUT - <EndDt> incorrect /to be skipped in BUYA and SLLE Option,Bertram Schon,2025-04-08 14:34:00,2025-04-28 09:53:00,NO,"EXRI0000004930When checking correctness of seev.031 OUT via SWIFT-testing tool, the below errors were received.Please note that the ""Election End"" should be skipped for BUYA and SLLE in seev.031 OUT anyway, as it is only a technical field (we do not enter this value). Only response deadline must be populated, as it is in MT564 OUT =>:13A::CAON//002:22F::CAOP//SLLE:11A::OPTN//EUR:17B::DFLT//N:98E::RDDT//20250408100000/02:16R:SECMOVEThanks, Bert",exriwhen checking correctness seev via swifttesting tool error receivedplease note election end skipped buya slle seev anyway technical field enter value response deadline must populated mt acaonfcaopslleaoptneurbdfltnerddtrsecmovethanks bert,-0.08389414101839066,19.0,51,0.4274149319487267,0.27097353525459766,10.***************,0.*****************,0.13,0.15,49.44%
Incident,OEKB-6781,Medium,M12 SCoRE: Std. 15 - Receiver BIC not available,Stefan RIBISCH,2025-04-03 11:33:00,2025-04-03 11:51:00,NO,"Event DVCA0000004883we sent notifications to all clients of this event and noticed that for 222100 which receives ISO20022 messages, the Receiver BIC is not filled in in MegaCor:Is this only a cosmetical topic or are there problems because of that?Please check that,thanks, stefan",event dvcawe sent notification client event noticed receives iso message receiver bic filled megacoris cosmetical topic problem thatplease check thatthanks stefan,-0.24206137284636497,0.0,56,0.39324072086859824,0.31051534321159124,18.**************,0.2,0.1,0.15,64.08%
Incident,OEKB-6780,Minor,M12 QAS: HIS Rule Error twice,Stefan RIBISCH,2025-04-03 11:12:00,2025-04-03 15:21:00,NO,"Event EXOF0000004880Today we created instructions for this event and one has been rejected immediately due to following error(s).Can you tell us why this error message is twice here?As these error messages are also forwarded to the Asset Servicing Online Client, the OCSD-Clients see this message also twice.Please ensure in general that error messages are not shown duplicated in the system.Thanks,stefan",event exoftoday created instruction event one rejected immediately due following errorscan tell u error message twice hereas error message also forwarded asset servicing online client ocsdclients see message also twiceplease ensure general error message shown duplicated systemthanksstefan,-0.4794399179518223,0.0,56,0.39324072086859824,0.36985997948795557,18.**************,0.2,0.07,0.15,68.48%
Incident,OEKB-6779,Major,M12 SCoRE - EXOF0000004880 - seev.031 OUT - <FrctnDspstn> incorrect,Bertram Schon,2025-04-03 09:43:00,2025-04-03 13:04:00,NO,"EXOF0000004880was set up with SECU IN Rounding In = DOWN (screenshot).However, in seev.031 OUT (attached),<FrctnDspstn> is mapped incorrectly =><OptnNb>001</OptnNb><OptnTp><Cd>SECU</Cd></OptnTp><FrctnDspstn>*<Cd>DIST</Cd>*</FrctnDspstn>Please check\!",exofwas set secu rounding screenshothowever seev attachedfrctndspstn mapped incorrectly optnnboptnnboptntpcdsecucdoptntpfrctndspstncddistcdfrctndspstnplease check,0.030590029433369637,0.0,56,0.39324072086859824,0.2423524926416576,10.***************,0.*****************,0.13,0.15,45.14%
Incident,OEKB-6778,Major,M12 QAS - EXOF0000004880 - Client Entitlements incorrect (also PRODUCTION issue?),Bertram Schon,2025-04-03 09:32:00,2025-04-03 16:09:00,NO,"EXOF0000004880SECU IN was set up with Rounding In = DOWN, Fraction Movement is not filled.Market Entitlements were calculated correctly (SECU IN quantity was rounded down), see screenshot 1.However, Client Entitlements were calculated incorrectly (SECU IN quantity contains fractions), see screenshot 2.Could you please check if this bug also exists in PRODUCTION environment, as I was testing a production event in QAS\!Thanks, Bert",exofsecu set rounding fraction movement filledmarket entitlement calculated correctly secu quantity rounded see screenshot however client entitlement calculated incorrectly secu quantity contains fraction see screenshot could please check bug also exists production environment testing production event qasthanks bert,-0.040352968499064445,0.0,56,0.39324072086859824,0.2600882421247661,10.***************,0.*****************,0.13,0.15,47.8%
Incident,OEKB-6770,Major,M12 SCoRE: Std. 15 - seev.031 for PROX event,Stefan RIBISCH,2025-04-01 11:09:00,2025-04-01 13:43:00,NO,"Hi Emna,I am afraid we have to overthink the logic of all the SLAs we created for OENB (and in the meantime also for 222100) for sending ISO20022.With the current configuration, the new SLAs have a higher weight and the template seev.031 is also taken for PROX events. I assume this would also happen for SHDS but I couldn't send a notification for that (see ticket OEKB-6769).I assume we need more SLAs than the 19 already created to ensure that for SRD II events the correct seev.message-templates are used.Do you deliver a suggest what is needed or should we set up a call to think and discuss about that together to avoid wrong message-flow in the future? If so, please send me your possible timeslots.Thanks,stefan",hi emnai afraid overthink logic slas created oenb meantime also sending isowith current configuration new slas higher weight template seev also taken prox event assume would also happen shds couldnt send notification see ticket oekbi assume need slas already created ensure srd ii event correct seevmessagetemplates useddo deliver suggest needed set call think discus together avoid wrong messageflow future please send possible timeslotsthanksstefan,0.007139984518289566,0.0,58,0.38034875658925854,0.2482150038704276,18.**************,0.2,0.13,0.15,59.23%
Incident,OEKB-6765,Major,M12 SCoRE: Std. 15 - outgoing seev.033 instruction comment not mapped,Stefan RIBISCH,2025-03-28 11:59:00,2025-04-03 18:43:00,NO,"event BIDS0000004814I consolidated the client instruction received via seev.033 and claimed for not mapping the instruction comment in OEKB-6764 and created the market instruction.After editing this MI and adding an INST comment I sent out the MI to the custodian. In the outgoing MT565 everything looks fine. In the seev.033 the instruction comment is again not mapped and not included.I attached both sent MI for you to this bug.Please check the problem and fix it.Thanks,stefan",event bidsi consolidated client instruction received via seev claimed mapping instruction comment oekb created market instructionafter editing mi adding inst comment sent mi custodian outgoing mt everything look fine seev instruction comment mapped includedi attached sent mi bugplease check problem fix itthanksstefan,0.019555769860744476,6.0,62,0.3558189185373419,0.24511105753481388,18.**************,0.2,0.13,0.15,58.77%
Incident,OEKB-6764,Major,M12 SCoRE: Std. 15 - incoming seev.033 - instruction comment not mapped,Stefan RIBISCH,2025-03-28 11:44:00,2025-04-03 18:43:00,NO,"event BIDS0000004814I instructed for above event with attached seev.033 instruction but unfortunately it got not Status InvalidData due to the comment but got Status Created (after the WaitingFreeze).This is wrong - the logic must be implemented as in ISO15022 where this already works (see other attached instruction).Please adapt that.Thanks,stefan",event bidsi instructed event attached seev instruction unfortunately got status invaliddata due comment got status created waitingfreezethis wrong logic must implemented iso already work see attached instructionplease adapt thatthanksstefan,-0.07250373810529709,6.0,62,0.3558189185373419,0.26812593452632427,18.**************,0.2,0.13,0.15,62.22%
Incident,OEKB-6763,Medium,"M12 SCoRE - RHDI0000004759 - seev.036 OUT - ""Intermediate Securities Distribution Type"" missing",Bertram Schon,2025-03-28 10:07:00,2025-03-28 10:36:00,NO,"RHDI0000004759When checking seev.036 (attached), I noticed that the ""Intermediate Securities Distribution Type"" is missing under ""Corporate Action Details"".I guess this must be populated under/Document/CorpActnMvmntConf/CorpActnDtls/IntrmdtSctiesDstrbtnTpFor comparison, see MT564 =>:16R:CADETL:98A::XDTE//20250318:98A::RDTE//20250319*:22F::RHDI//EXRI*:16S:CADETLCould you please check?Thanks, Bert",rhdiwhen checking seev attached noticed intermediate security distribution type missing corporate action detailsi guess must populated underdocumentcorpactnmvmntconfcorpactndtlsintrmdtsctiesdstrbtntpfor comparison see mt rcadetlaxdteardtefrhdiexriscadetlcould please checkthanks bert,-0.04178113490343094,0.0,62,0.3558189185373419,0.26044528372585773,10.***************,0.*****************,0.1,0.15,43.36%
Incident,OEKB-6762,Major,M12 QAS: Repair invalid instruction not possible,Stefan RIBISCH,2025-03-28 08:37:00,2025-03-28 12:59:00,NO,"Event BIDS0000004814Instruction MT565 with text has been received and got in Status InvalidData - which is correct.When repairing the instruction I wanted to open the instruction comment (which was visible twice - maybe you could check that too) I got following error message.Nevertheless I could deactivate the Narrative check field and could repair the instruction.Please check what is the problem with that error message.Thanks,stefan",event bidsinstruction mt text received got status invaliddata correctwhen repairing instruction wanted open instruction comment visible twice maybe could check got following error messagenevertheless could deactivate narrative check field could repair instructionplease check problem error messagethanksstefan,-0.3151366636157036,0.0,62,0.3558189185373419,0.3287841659039259,18.**************,0.2,0.13,0.15,71.32%
Incident,OEKB-6760,Medium,M12 SCoRE - EXRI0000004760 - seev.031 OUT - <AddtlInf> contains strange characters,Bertram Schon,2025-03-27 15:09:00,2025-04-02 14:06:00,NO,"EXRI0000004760When checking Additional Info, I noticed that there are lots of strange characters (see screenshot).Is this only a display issue or will these characters be available in the message?Could you please check?Thanks, Bert",exriwhen checking additional info noticed lot strange character see screenshotis display issue character available messagecould please checkthanks bert,-0.010069070383906364,5.0,63,0.3499377491111553,0.2525172675959766,10.***************,0.*****************,0.1,0.15,42.17%
Incident,OEKB-6759,Major,M12 SCoRE - EXRI0000004760 - seev.031 OUT - SLLE/BUYA Option - Mapping errors,Bertram Schon,2025-03-27 15:02:00,2025-04-03 18:44:00,NO,"EXRI0000004760Could you please check and correct the following mapping errors =>SLLE/BUYA Option:* Please skip Market Deadline as there is no such value available =><MktDdln><DtCd><Cd>UKWN</Cd></DtCd></MktDdln>BUYA Option:* <CshMvmntDtls><CdtDbtInd>CRDT</CdtDbtInd> is incorrect (must be DBIT)Thanks, Bert",exricould please check correct following mapping error sllebuya option please skip market deadline value available mktddlndtcdcdukwncddtcdmktddlnbuya option cshmvmntdtlscdtdbtindcrdtcdtdbtind incorrect must dbitthanks bert,-0.030246511101722717,7.0,63,0.3499377491111553,0.2575616277754307,10.***************,0.*****************,0.13,0.15,47.42%
Incident,OEKB-6758,Major,M12 SCoRE - EXRI0000004760 - seev.031 OUT - EXER Option - <CshMvmntDtls> incorrect/incomplete,Bertram Schon,2025-03-27 14:48:00,2025-04-03 18:45:00,NO,"EXRI0000004760Please check <CshMvmntDtls> in EXER option =>* <CdtDbtInd> CRDT is incorrect (must be DBIT)* Exercise Price is missingFor comparison, see MT564 =>:16R:CASHMOVE:22H::CRDB//DEBT:98A::PAYD//20250324:90B::PRPP//ACTU/EUR3,17:16S:CASHMOVEBoth seev.031 and MT564 attached.Thanks, Bert",exriplease check cshmvmntdtls exer option cdtdbtind crdt incorrect must dbit exercise price missingfor comparison see mt rcashmovehcrdbdebtapaydbprppactueurscashmoveboth seev mt attachedthanks bert,0.025005098432302475,7.0,63,0.3499377491111553,0.24374872539192438,10.***************,0.*****************,0.13,0.15,45.35%
Incident,OEKB-6757,Medium,M12 SCoRE - EXRI0000004760 - seev.031 OUT should not include <RcrdDt> UKWN if there is no Record Date,Bertram Schon,2025-03-27 14:33:00,2025-04-03 18:47:00,NO,"EXRI0000004760When checking seev.031 OUT (attached), I noticed that Record Date is populated as unknown =><RcrdDt><DtCd><Cd>UKWN</Cd></DtCd></RcrdDt>As there is no Record Date at all, this field should be skipped in the message.Please check\!Thanks, Bert",exriwhen checking seev attached noticed record date populated unknown rcrddtdtcdcdukwncddtcdrcrddtas record date field skipped messageplease checkthanks bert,-0.0206527728587389,7.0,63,0.3499377491111553,0.2551631932146847,10.***************,0.*****************,0.1,0.15,42.56%
Incident,OEKB-6756,Critical,M12 SCoRE: Std. 15 - incoming seev.031 not totally mapped into MegaCor,Stefan RIBISCH,2025-03-27 14:01:00,2025-03-27 14:05:00,NO,"Today I created BIDS event BIDS0000004814 via incoming SWIFT seev.031 from CBF.Unfortunately much information was not mapped into MegaCor although value was included in the seev.031.You find the SWIFT seev.031 attached and also screens of the event in MegaCorFollowing fields have no value in MegaCor but should have a value:* Comments for Notification: OFFO* Comments for Notification: WEBB* Option 001 and 002: Option Feature: BOIS* Option 001 and 002: Instruction Processing Info: MIEX and MILT* Option 001: SEC OUT: ONGO must be YES* Option 001 and 002: CASH IN: Price* Option 001 and 002 and 003: Option Comment: INCO* Event Incoming Comment: ADTX* Event Incoming Comment: COMPPlease check these fields and make sure that everything which is included in the incoming seev.031 is mapped, as it is also for incoming MT564 SWIFTs.Thanks,stefan",today created bid event bid via incoming swift seev cbfunfortunately much information mapped megacor although value included seevyou find swift seev attached also screen event megacorfollowing field value megacor value comment notification offo comment notification webb option option feature bois option instruction processing info miex milt option sec ongo must yes option cash price option option comment inco event incoming comment adtx event incoming comment compplease check field make sure everything included incoming seev mapped also incoming mt swiftsthanksstefan,-0.013891113921999931,0.0,63,0.3499377491111553,0.2534727784805,18.**************,0.2,0.15,0.15,63.02%
Incident,OEKB-6755,Medium,M12 SCoRE: Std. 15 - Fraction Disposition should not be included,Stefan RIBISCH,2025-03-27 13:17:00,2025-04-03 18:45:00,NO,"Event BIDS0000004813 (Superevent BIDS).In the outgoing seev.031 I found the following part in option 001/CASH and 002/CASH:_<FrctnDspstn>__<Cd>DIST</Cd>__</FrctnDspstn>_This part is not included in the outgoing MT564.As there is no SECU IN part in these two options this tag shouldn't be included in the outgoing seev.031.Please adapt the ISO20022-behaviour according to the ISO15022-behaviour.Thanks,stefan",event bid superevent bidsin outgoing seev found following part option cash cashfrctndspstncddistcdfrctndspstnthis part included outgoing mtas secu part two option tag shouldnt included outgoing seevplease adapt isobehaviour according isobehaviourthanksstefan,0.06537369824945927,7.0,63,0.3499377491111553,0.23365657543763518,18.**************,0.2,0.1,0.15,52.55%
Incident,OEKB-6754,Medium,M12 SCoRE: Std. 15 - no UKWN-deadlines when no deadlines existing,Stefan RIBISCH,2025-03-27 13:04:00,2025-04-03 18:48:00,NO,"At event BIDS0000004813 (Superevent as BIDS) there is option 003/NOAC. No deadlines are included in MegaCor (see attachment). But in the outgoing seev.031 there are parts for both market and client deadline with value UKWN. In the MT564 there are no lines for these dates.Attached you find the ISO15022 and ISO20022 message for this event.Please adapt ISO20022 according to the behaviour for ISO15022 - if there are no deadlines, no UKWN-dates are needed.Thanks,stefan",event bid superevent bid option noac deadline included megacor see attachment outgoing seev part market client deadline value ukwn mt line datesattached find iso iso message eventplease adapt iso according behaviour iso deadline ukwndates neededthanksstefan,0.03456926718354225,7.0,63,0.3499377491111553,0.24135768320411444,18.**************,0.2,0.1,0.15,53.7%
Incident,OEKB-6753,Minor,M12 QAS: Certification Breakdown Type not visible,Stefan RIBISCH,2025-03-27 12:52:00,,NO,"We noticed that the Certification Breakdown Type is not visible in the view screen of an event and in the update screens it is visible sometimes and sometimes not. We checked it in different events (BIDS0000004813, BIDS0000004814).Attached you find the view screen of both events which look the same. But you also see the difference in the update-screens.Please correct that to see this field in the normal view screen and also in the update mode. In outgoing SWIFTs this field is included.Thanks,stefan",noticed certification breakdown type visible view screen event update screen visible sometimes sometimes checked different event bid bidsattached find view screen event look also see difference updatescreensplease correct see field normal view screen also update mode outgoing swift field includedthanksstefan,-0.0100515466183424,0.0,63,0.3499377491111553,0.2525128866545856,18.**************,0.2,0.07,0.15,50.88%
Incident,OEKB-6752,Medium,M12 QAS: SEC OUT not visible in Superevent BIDS,Stefan RIBISCH,2025-03-27 12:39:00,,NO,"Although there is definitively a SEC OUT section included in both CASH options of the following two events: BIDS0000004812 and BIDS0000004813 they are not shown in the VIEW screen (see attachment). Only in the update screen (second screenshot) and they are also included in the outgoing messages to the clients (you find examples attached).Why are these SEC OUT sections not visible in the view screen? A refresh and also close and reopen of MegaCor didn’t help.Please check that.Thanks,stefan",although definitively sec section included cash option following two event bid bid shown view screen see attachment update screen second screenshot also included outgoing message client find example attachedwhy sec section visible view screen refresh also close reopen megacor didnt helpplease check thatthanksstefan,0.021508714184165,0.0,63,0.3499377491111553,0.24462282145395875,18.**************,0.2,0.1,0.15,54.19%
Incident,OEKB-6751,Major,M12 SCoRE: Std. 15 - seev.031 generation failed,Stefan RIBISCH,2025-03-27 09:44:00,,NO,"Event BIDS0000004812Event has an ADTX outgoing comment with more than 8000 characters. For all ISO15022 messages MegaCor creates as many 70E//ADTX blocks as needed but for ISO20022 the generation has failed in MegaBroker:Please make sure that also longer text can be sent via seev messages.Thanks,stefan",event bidsevent adtx outgoing comment character iso message megacor creates many eadtx block needed iso generation failed megabrokerplease make sure also longer text sent via seev messagesthanksstefan,-0.0018047112971544266,0.0,63,0.3499377491111553,0.2504511778242886,18.**************,0.2,0.13,0.15,59.57%
Incident,OEKB-6748,Major,M12 SCoRE: Std. 15 - FAMT instead of UNIT,Stefan RIBISCH,2025-03-26 15:33:00,2025-03-27 08:34:00,NO,"Today I sent out a market instruction to custodian CBF. According to Custodian Reporting SLA both messages MT565 and seev.033 were generated.Unfortunately in the outgoing seev.033 MegaCor created the SWIFT with FACE AMOUNT instead of UNIT. In MT565 it correctly took UNIT.Please correct that.Thanks,stefan",today sent market instruction custodian cbf according custodian reporting sla message mt seev generatedunfortunately outgoing seev megacor created swift face amount instead unit mt correctly took unitplease correct thatthanksstefan,0.*****************,0.0,64,0.*****************,0.*****************,18.**************,0.2,0.13,0.15,51.3%
Incident,OEKB-6747,Medium,M12 SCoRE: Std. 15 - OCSD-Prefix-Logic for seev.033,Stefan RIBISCH,2025-03-26 15:28:00,2025-03-27 09:28:00,NO,"Today I created event TEND0000004807 and sent in two instructions (you find them attached). The difference of them: one was sent for account OCSD205400 and the other for 205400.Unfortunately MegaCor rejected the instruction with OCSD205400 and created it for 205400 but the behaviour should be exactly the opposite: We expect from clients OCSD205400 and without prefix, the instruction should be rejected.Please implement this logic for ISO20022 as it already works for ISO15022.Thanks,stefan",today created event tend sent two instruction find attached difference one sent account ocsd unfortunately megacor rejected instruction ocsd created behaviour exactly opposite expect client ocsd without prefix instruction rejectedplease implement logic iso already work isothanksstefan,0.*****************,0.0,64,0.*****************,0.*****************,18.**************,0.2,0.1,0.15,54.67%
Incident,OEKB-6745,Medium,M12 SCoRE - BONU0000004763 - seev.036 OUT - Cash In Lieu Of Share Price missing,Bertram Schon,2025-03-26 08:32:00,2025-03-26 09:20:00,NO,"BONU0000004763When checking seev.036 OUT (attached), I noticed that there is no ""Cash In Lieu Of Share Price"" available.In the MT566 OUT, this is populated as follows =>:90B::CINL//ACTU/EUR48,55I suppose, in the seev.036 OUT, the relevant field is =>/Document/CorpActnMvmntConf/CorpActnConfDtls/PricDtls/CshInLieuOfShrPricCould you please check?Thanks, Bert",bonuwhen checking seev attached noticed cash lieu share price availablein mt populated follows bcinlactueuri suppose seev relevant field documentcorpactnmvmntconfcorpactnconfdtlspricdtlscshinlieuofshrpriccould please checkthanks bert,0.029413187876343727,0.0,64,0.*****************,0.24264670303091407,10.***************,0.*****************,0.1,0.15,40.69%
Incident,OEKB-6737,Major,M12 SCoRE - EXRI0000004760 - Generation of seev.031 failed,Bertram Schon,2025-03-20 11:31:00,2025-03-21 11:51:00,NO,"EXRI0000004760I tried to send seev.031 for client 229500.However, the generation failed with error message =>""Cannot invoke ""\[B.clone()"" because ""content"" is null""I guess the issue is again related to the outgoing comment as reported in ticket OEKB-6710.Could you please make sure to fix it for all type of events?Thanks, Bert",exrii tried send seev client however generation failed error message invoke bclone content nulli guess issue related outgoing comment reported ticket oekbcould please make sure fix type eventsthanks bert,-0.20490822941064835,1.0,70,0.3114032239145977,0.3012270573526621,10.***************,0.*****************,0.13,0.15,53.97%
Incident,OEKB-6736,Medium,M12 SCoRE - RHDI0000004759 - seev.031 OUT - <FrctnDspstn> not included,Bertram Schon,2025-03-20 10:22:00,,NO,"RHDI0000004759I checked seev.031 OUT and noticed that the tag <FrctnDspstn> is not included in the message.In this case, ADEX is 1 for 1, but in MT564 we still have:22F::DISF//DIST:92D::ADEX//1,/1,in the outgoing 564.Could you please check if <FrctnDspstn> should be available in seev.031, even if <QtyToQty> = 1 for 1?Thanks, Bert",rhdii checked seev noticed tag frctndspstn included messagein case adex mt still havefdisfdistdadexin outgoing could please check frctndspstn available seev even qtytoqty thanks bert,0.055266451090574265,0.0,70,0.3114032239145977,0.23618338722735643,10.***************,0.*****************,0.1,0.15,39.72%
Incident,OEKB-6734,Medium,M12 QAS - Create CA / Input SPLR MAND failed,Bertram Schon,2025-03-20 10:09:00,2025-03-20 10:21:00,NO,"Hi,I want to create SPLR manually via “Input CA”.After entering all data and trying to save the following error popped up (see screenshot):PALM-01371: The value (SPLR) for the field (Ca Code) for the entity ([com.vermeg.mcca.CA|http://com.vermeg.mcca.CA]) of code (SPLR0000004762) is not validAlso, I see that the ca code SPLR was not assigned to the input screen and cannot be added, I guess that’s why the event cannot be saved.Please check\!Thanks, Bert",hii want create splr manually via input caafter entering data trying save following error popped see screenshotpalm value splr field ca code entity comvermegmccaca code splr validalso see ca code splr assigned input screen added guess thats event savedplease checkthanks bert,-0.006340540945529938,0.0,70,0.3114032239145977,0.2515851352363825,10.***************,0.*****************,0.1,0.15,42.03%
Incident,OEKB-6725,Medium,M12 SCoRE - TEND0000004716 - seev.031 - NOAC Option - Option Currency to be skipped,Bertram Schon,2025-03-17 15:15:00,2025-03-17 15:28:00,NO,"TEND0000004716When checking seev.031 OUT (attached), I noticed that currency option (CcyOptn) is populated under NOAC Option =><CorpActnOptnDtls><OptnNb>002</OptnNb><OptnTp><Cd>NOAC</Cd></OptnTp><CcyOptn>EUR</CcyOptn>Could you please skip CcyOptn for options where no cash payment will take place.Thanks, Bert",tendwhen checking seev attached noticed currency option ccyoptn populated noac option corpactnoptndtlsoptnnboptnnboptntpcdnoaccdoptntpccyoptneurccyoptncould please skip ccyoptn option cash payment take placethanks bert,0.030627388507127762,0.0,73,0.29621590947194976,0.24234315287321806,10.***************,0.*****************,0.1,0.15,40.64%
Incident,OEKB-6724,Major,M12 SCoRE - TEND0000004716 - seev.031 - Offeror not mapped,Bertram Schon,2025-03-17 15:05:00,2025-03-17 15:29:00,NO,"TEND0000004716When checking seev.031 OUT (attached), I noticed that the Offeror (OFFO) is not populated although this info is available in the event data (see screenshot).In the seev.031 OUT, only WEBB is available under <AddtlInf> =><AddtlInf><URLAdr><Lang>en</Lang><URLAdr>[https://robau-beteiligung.at/|https://robau-beteiligung.at/]</URLAdr></URLAdr></AddtlInf>Could you please check?Thanks, Bert",tendwhen checking seev attached noticed offeror offo populated although info available event data see screenshotin seev webb available addtlinf addtlinfurladrlangenlangurladr please checkthanks bert,0.014490967616438866,0.0,73,0.29621590947194976,0.24637725809589028,10.***************,0.*****************,0.13,0.15,45.75%
Incident,OEKB-6719,Medium,M12 SCoRE - TEND0000004716 - UTC time in seev.031,Bertram Schon,2025-03-14 11:09:00,2025-03-14 11:18:00,NO,"TEND0000004716In the seev.031 OUT, deadlines are populated in the following format:<DtTm>2025-05-20T16:00:00.000Z</DtTm>As agreed during M12 testing, the requested format for ISO 20022 messages isD*tTm incl. local time + main entity UTC indicator*(i.e.: <DtTm>2025-05-20T17:00:00+01:00</DtTm>)Could you please check and make sure that all time fields are mapped in the same correct format?Thanks, Bert",tendin seev deadline populated following formatdttmtzdttmas agreed testing requested format iso message isdttm incl local time main entity utc indicatorie dttmtdttmcould please check make sure time field mapped correct formatthanks bert,0.02664695493876934,0.0,76,0.28176928909495835,0.24333826126530766,10.***************,0.*****************,0.1,0.15,40.79%
Incident,OEKB-6717,Medium,M12 SCoRE: Std. 13 - no MT564 generated - too long narrative text,Stefan RIBISCH,2025-03-14 10:32:00,2025-03-14 10:52:00,NO,"I created a reversal request for event DVCA0000003884 with three clients which receive ISO 15022 message MT564.According to SWIFT, the length of the narrative field is 6*35 characters (= 210) but in reversal request input screen you can fill in 255 characters (which is allowed for ISO 20022 seev.035 REVR).As a consequence: The MT564 are not generated now.Please find a solution that only 210 characters are possible in narrative field - IF that is the reason for the not-generated notifications.Thanks,stefan",created reversal request event dvca three client receive iso message mtaccording swift length narrative field character reversal request input screen fill character allowed iso seev revras consequence mt generated nowplease find solution character possible narrative field reason notgenerated notificationsthanksstefan,0.006912499666213989,0.0,76,0.28176928909495835,0.2482718750834465,18.**************,0.2,0.1,0.15,54.74%
Incident,OEKB-6711,Medium,M12 SCoRE - TEND0000004716 - No seev.034 sent to client,Bertram Schon,2025-03-13 14:30:00,2025-03-13 14:45:00,NO,"TEND0000004716I created a client instruction.When checking the Instruction Status Advice, no message (seev.034) is available under “View Message”.Can you please check?Thanks, Bert",tendi created client instructionwhen checking instruction status advice message seev available view messagecan please checkthanks bert,0.008105982095003128,0.0,77,0.2771120523885023,0.24797350447624922,10.***************,0.*****************,0.1,0.15,41.49%
Incident,OEKB-6710,Medium,M12 SCoRE - TEND0000004716 - Generation of seev.031 failed,Bertram Schon,2025-03-13 13:39:00,2025-03-13 13:41:00,NO,"TEND0000004716I tried to send seev.031 for client 229500.However, the generation failed with error message =>""Cannot invoke ""\[B.clone()"" because ""content"" is null""Please check\!Thanks, Bert",tendi tried send seev client however generation failed error message invoke bclone content nullplease checkthanks bert,-0.6006665788590908,0.0,77,0.2771120523885023,0.4001666447147727,10.***************,0.*****************,0.1,0.15,64.31%
Incident,OEKB-6693,Medium,M12 QAS - PROX0000004640 - seev.004 not available in MegaCor,Bertram Schon,2025-03-05 13:24:00,2025-03-05 13:27:00,NO,"PROX0000004640I wanted to import seev.004, status in MegaBroker is “ValidFlow”, but the instruction is not available in MegaCor.FlowIn, FlowOut attached.Could you please check?Thanks, Bert",proxi wanted import seev status megabroker validflow instruction available megacorflowin flowout attachedcould please checkthanks bert,0.02352479100227356,0.0,85,0.2425210746356487,0.2441188022494316,10.***************,0.*****************,0.1,0.15,40.91%
Incident,OEKB-6691,Medium,M12 QAS - SOFF0000004560 - Validation of update failed,Bertram Schon,2025-03-03 14:39:00,2025-03-03 14:41:00,NO,"SOFF0000004560I set up a SOFF event by importing MT564 (attached).After saving, Ana tried to validate, but the below error popped up.Please check\!Thanks, Bert",soffi set soff event importing mt attachedafter saving ana tried validate error popped upplease checkthanks bert,0.12490949779748917,0.0,87,0.23457028809379765,0.2187726255506277,10.***************,0.*****************,0.1,0.15,37.11%
