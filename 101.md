# Vermeg Insights - How to Run the Web Application

This guide provides step-by-step instructions on how to set up and run the Vermeg Insights web application.

## Prerequisites

Before starting, ensure you have the following installed:
- Python 3.8 or higher
- pip (Python package manager)
- Git (optional, for cloning the repository)

## Setup Instructions

### 1. Set Up the Environment

#### Create and Activate a Virtual Environment

**Windows:**
```
python -m venv venv
venv\Scripts\activate
```

**Linux/Mac:**
```
python -m venv venv
source venv/bin/activate
```

### 2. Install Dependencies

Install all required packages:
```
pip install -r requirements.txt
```

### 3. Database Setup

The application uses SQLite by default, which requires no additional setup.

If you want to use PostgreSQL (optional):
1. Install PostgreSQL
2. Create a database
3. Update the `.env` file with your database credentials

### 4. Run Migrations

Apply database migrations:
```
python manage.py migrate
```

### 5. Create an Admin User (Optional)

Create a superuser to access the admin panel:
```
python manage.py createsuperuser
```
Follow the prompts to set up your admin username and password.

## Running the Web Application

### Start the Development Server

To start the web application, run:
```
python manage.py runserver
```

This will start the development server atr

### Accessing the Web Application

1. Open your web browser
2. Navigate to http://127.0.0.1:8000/
3. You will be directed to the login page
4. Log in with your credentials

## Application Features

- **Home**: Overview of the application and NLP analysis methodology
- **Client Overview**: View client metrics and analysis results
- **Upload**: Upload JIRA data files for analysis
- **Analysis**: View detailed analysis of processed files

## Stopping the Web Application

To stop the web server, press `CTRL+C` in the terminal where the server is running.

## Troubleshooting

### Common Issues

1. **Port already in use**
   - If port 8000 is already in use, you can specify a different port:
   ```
   python manage.py runserver 8080
   ```

2. **Missing dependencies**
   - If you encounter errors about missing packages, ensure you've activated your virtual environment and installed all dependencies:
   ```
   pip install -r requirements.txt
   ```

3. **Database errors**
   - If you encounter database errors, try resetting the database:
   ```
   python manage.py migrate --run-syncdb
   ```

## Additional Resources

- Django documentation: https://docs.djangoproject.com/
- For more information about the application, refer to the project documentation
