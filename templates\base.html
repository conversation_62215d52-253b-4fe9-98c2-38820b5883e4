{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Vermeg Insights{% endblock %}</title>
    <!-- Ensure the title is correctly set -->
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{% static 'images/favicon.png' %}">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            --primary: #e31937;
            --primary-dark: #c01730;
            --secondary: #333333;
            --accent: #e31937;
            --text-dark: #000000;
            --text-light: #6c757d;
            --white: #ffffff;
            --success: #28a745;
            --warning: #ffc107;
            --danger: #e31937;
            --light: #f8f9fa;
            --dark: #000000;
            --gray-light: #f0f0f0;
            --gray-medium: #d0d0d0;
            --gray-dark: #333333;
            --border-radius: 0.375rem;
            --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        body {
            font-family: 'Open Sans', sans-serif;
            padding-top: 80px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            color: var(--text-dark);
            background-color: var(--white);
        }

        .content {
            flex: 1;
        }

        .footer {
            padding: 1.5rem 0;
            background-color: var(--dark);
            color: var(--white);
            margin-top: auto;
            border-top: 1px solid var(--gray-dark);
        }

        .footer a {
            color: var(--gray-medium) !important;
        }

        .footer a:hover {
            color: var(--white) !important;
        }

        /* Navbar styling */
        .navbar {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            background-color: var(--dark) !important;
            padding: 0.5rem 1rem;
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--white) !important;
            display: flex;
            align-items: center;
        }

        .navbar-brand img {
            height: 20px;
            margin-right: 5px;
            width: auto;
        }

        .navbar-nav .nav-link {
            color: var(--white) !important;
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: color 0.2s;
            font-size: 0.95rem;
        }

        .navbar-nav .nav-link:hover {
            color: var(--primary) !important;
        }

        .navbar-nav .nav-link.active {
            color: var(--primary) !important;
        }

        .navbar-toggler {
            background-color: var(--gray-light);
        }

        .btn-danger {
            background-color: var(--primary);
            border-color: var(--primary);
        }

        .btn-danger:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
        }

        /* Card styling */
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
            background-color: var(--white);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .card-header {
            background-color: var(--white);
            color: var(--text-dark);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            font-weight: 600;
        }

        /* Button styling */
        .btn {
            border-radius: var(--border-radius);
            font-weight: 600;
            padding: 0.5rem 1.25rem;
            transition: all 0.2s;
        }

        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
        }

        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary);
            border-color: var(--primary);
        }

        /* Alert styling */
        .alert {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        /* Table styling */
        .table {
            --bs-table-hover-bg: rgba(227, 25, 55, 0.05);
        }

        .table thead th {
            background-color: var(--dark);
            color: var(--white);
            font-weight: 600;
            border-bottom: none;
        }

        /* Chart containers */
        .chart-container {
            height: 300px;
            margin-bottom: 30px;
        }

        /* Badge styling */
        .badge {
            font-weight: 600;
            padding: 0.5em 0.75em;
        }

        /* Form styling */
        .form-control {
            border-radius: var(--border-radius);
            padding: 0.5rem 0.75rem;
            border: 1px solid #ced4da;
        }

        .form-control:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(227, 25, 55, 0.25);
        }

        /* Page headers */
        .page-header {
            padding-bottom: 1rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid var(--secondary);
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .animate-fade-in {
            animation: fadeIn 0.5s;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="{% url 'home' %}">
                <span style="font-size: 20px; font-weight: bold; color: #ffffff;">VERMEG INSIGHTS</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    {% if user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == '/overview/' %}active{% endif %}" href="{% url 'overview' %}">
                            Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == '/dashboard/' %}active{% endif %}" href="{% url 'dashboard' %}">
                            Analysis
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/client-overview/' in request.path %}active{% endif %}" href="{% url 'client_overview' %}">
                            Client Overview
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/team-overview/' in request.path %}active{% endif %}" href="{% url 'team_overview' %}">
                            Team Overview
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if '/ai-agent/' in request.path %}active{% endif %}" href="{% url 'ai_agent' %}">
                            AI Agent
                        </a>
                    </li>
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            {{ user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li>
                                <a class="dropdown-item" href="{% url 'password_change' %}">
                                    Change Password
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{% url 'logout' %}">
                                    Logout
                                </a>
                            </li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'login' %}">
                            Login
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-danger text-white px-3 ms-2" href="{% url 'register' %}">
                            Contact us
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Content wrapper -->
    <div class="content animate-fade-in">
        <!-- Messages -->
        {% if messages %}
        <div class="container mt-3">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                {% if message.tags == 'success' %}
                    <i class="fas fa-check-circle me-2"></i>
                {% elif message.tags == 'error' %}
                    <i class="fas fa-exclamation-circle me-2"></i>
                {% elif message.tags == 'warning' %}
                    <i class="fas fa-exclamation-triangle me-2"></i>
                {% elif message.tags == 'info' %}
                    <i class="fas fa-info-circle me-2"></i>
                {% endif %}
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Main Content -->
        <div class="container mt-4">
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0">
                        <a href="#" class="text-decoration-none">
                            <span style="font-size: 16px; font-weight: bold; color: #ffffff;">VERMEG INSIGHTS</span>
                            <span style="color: #d0d0d0;">&copy; {% now "Y" %} Vermeg</span>
                        </a>
                    </p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <a href="#" class="text-decoration-none me-3">
                        <i class="fas fa-question-circle"></i> Help
                    </a>
                    <a href="#" class="text-decoration-none me-3">
                        <i class="fas fa-shield-alt"></i> Privacy
                    </a>
                    <a href="#" class="text-decoration-none">
                        <i class="fas fa-file-contract"></i> Terms
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js for visualizations -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Common script -->
    <script>
        // Add active class to current nav item
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if (href === currentPath) {
                    link.classList.add('active');
                }
            });
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>