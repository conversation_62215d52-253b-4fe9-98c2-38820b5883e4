<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Global Sentiment Chart Test</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div style="width: 800px; height: 400px; margin: 50px auto;">
        <h2>Global Sentiment Analysis Chart Test</h2>
        <canvas id="globalSentimentChart"></canvas>
    </div>

    <script>
        // Test data based on the actual data from the database
        const sentimentData = {
            'Valdes ELIZABEHT': {
                current_sentiment: -0.31184363157416767,
                trend: 'declining',
                severity: 'medium',
                last_update: '2025-05-30',
                tickets: 13,
                resolution_time: 2.15,
                data_points: 3
            },
            'Sonja TAGHIPOUR': {
                current_sentiment: -0.30115102864801885,
                trend: 'stable',
                severity: 'medium',
                last_update: '2025-05-30',
                tickets: 5,
                resolution_time: 0.0,
                data_points: 1
            },
            '<PERSON> Maria G<PERSON>': {
                current_sentiment: -0.24637378697904447,
                trend: 'improving',
                severity: 'medium',
                last_update: '2025-05-30',
                tickets: 12,
                resolution_time: 0.08,
                data_points: 3
            },
            'christina HUEBNER': {
                current_sentiment: -0.19219518452882767,
                trend: 'stable',
                severity: 'low',
                last_update: '2025-05-30',
                tickets: 5,
                resolution_time: 0.0,
                data_points: 1
            },
            'Wallmann GEROLD': {
                current_sentiment: -0.184376038176318,
                trend: 'declining',
                severity: 'low',
                last_update: '2025-05-30',
                tickets: 45,
                resolution_time: 0.96,
                data_points: 3
            },
            'Stefan RIBISCH': {
                current_sentiment: -0.1246395396689574,
                trend: 'improving',
                severity: 'low',
                last_update: '2025-05-30',
                tickets: 3,
                resolution_time: 0.0,
                data_points: 4
            },
            'Bertram Schon': {
                current_sentiment: -0.11638852953910828,
                trend: 'improving',
                severity: 'low',
                last_update: '2025-05-30',
                tickets: 3,
                resolution_time: 3.33,
                data_points: 5
            }
        };

        function createGlobalSentimentChart() {
            const ctx = document.getElementById('globalSentimentChart').getContext('2d');

            // Convert to array and sort by sentiment (most negative first)
            const clientSentimentArray = Object.entries(sentimentData).map(([client, data]) => {
                return {
                    client: client,
                    sentiment: data.current_sentiment,
                    trend: data.trend,
                    severity: data.severity,
                    lastUpdate: data.last_update,
                    tickets: data.tickets,
                    resolutionTime: data.resolution_time,
                    dataPoints: data.data_points
                };
            });

            // Sort by sentiment (most negative first) and take top 7
            const sortedClients = clientSentimentArray.sort((a, b) => a.sentiment - b.sentiment);
            const topNegativeClients = sortedClients.slice(0, 7);

            // Prepare chart data
            const labels = topNegativeClients.map(client => client.client);
            const sentimentValues = topNegativeClients.map(client => client.sentiment);

            // Create background colors based on severity
            const backgroundColors = topNegativeClients.map(client => {
                switch(client.severity) {
                    case 'critical': return 'rgba(220, 53, 69, 0.8)'; // Red
                    case 'high': return 'rgba(255, 99, 132, 0.8)'; // Light Red
                    case 'medium': return 'rgba(255, 159, 64, 0.8)'; // Orange
                    case 'low': return 'rgba(255, 205, 86, 0.8)'; // Yellow
                    default: return 'rgba(75, 192, 192, 0.8)'; // Green
                }
            });

            // Create border colors (darker versions)
            const borderColors = topNegativeClients.map(client => {
                switch(client.severity) {
                    case 'critical': return 'rgba(220, 53, 69, 1)';
                    case 'high': return 'rgba(255, 99, 132, 1)';
                    case 'medium': return 'rgba(255, 159, 64, 1)';
                    case 'low': return 'rgba(255, 205, 86, 1)';
                    default: return 'rgba(75, 192, 192, 1)';
                }
            });

            // Create the chart
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Sentiment Score',
                        data: sentimentValues,
                        backgroundColor: backgroundColors,
                        borderColor: borderColors,
                        borderWidth: 2,
                        borderRadius: 4,
                        borderSkipped: false,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: {
                        duration: 2000,
                        easing: 'easeInOutQuart'
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: 'white',
                            bodyColor: 'white',
                            borderColor: 'rgba(255, 255, 255, 0.2)',
                            borderWidth: 1,
                            callbacks: {
                                title: function(context) {
                                    const clientIndex = context[0].dataIndex;
                                    const client = topNegativeClients[clientIndex];
                                    return client.client;
                                },
                                label: function(context) {
                                    const clientIndex = context.dataIndex;
                                    const client = topNegativeClients[clientIndex];

                                    // Get trend emoji
                                    let trendEmoji = '➖';
                                    if (client.trend === 'improving') trendEmoji = '✅';
                                    else if (client.trend === 'declining') trendEmoji = '❌';

                                    // Get severity emoji
                                    let severityEmoji = '⚪️';
                                    if (client.severity === 'critical') severityEmoji = '🚨';
                                    else if (client.severity === 'high') severityEmoji = '🔴';
                                    else if (client.severity === 'medium') severityEmoji = '🟠';
                                    else if (client.severity === 'low') severityEmoji = '🟡';

                                    return [
                                        `Sentiment: ${client.sentiment.toFixed(3)} ${severityEmoji}`,
                                        `Trend: ${client.trend} ${trendEmoji}`,
                                        `Tickets: ${client.tickets}`,
                                        `Avg Resolution: ${client.resolutionTime.toFixed(1)} days`,
                                        `Last Update: ${client.lastUpdate}`,
                                        `Data Points: ${client.dataPoints}`
                                    ];
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: Math.min(...sentimentValues) - 0.1,
                            max: 0.1,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(2);
                                }
                            },
                            title: {
                                display: true,
                                text: 'Sentiment Score'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                maxRotation: 45,
                                minRotation: 0
                            }
                        }
                    }
                }
            });
        }

        // Initialize the chart when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            createGlobalSentimentChart();
        });
    </script>
</body>
</html>
