{% extends 'base.html' %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Change Password</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.errors %}
                    <div class="alert alert-danger">
                        <strong>Please correct the errors below:</strong>
                        <ul>
                            {% for field in form %}
                                {% for error in field.errors %}
                                <li>{{ field.label }}: {{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label for="id_old_password" class="form-label">Current Password</label>
                        <input type="password" name="old_password" class="form-control {% if form.old_password.errors %}is-invalid{% endif %}" id="id_old_password" required>
                        {% if form.old_password.errors %}
                        <div class="invalid-feedback">
                            {{ form.old_password.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="id_new_password1" class="form-label">New Password</label>
                        <input type="password" name="new_password1" class="form-control {% if form.new_password1.errors %}is-invalid{% endif %}" id="id_new_password1" required>
                        {% if form.new_password1.errors %}
                        <div class="invalid-feedback">
                            {{ form.new_password1.errors.0 }}
                        </div>
                        {% endif %}
                        <div class="form-text">
                            <ul class="small text-muted">
                                <li>Your password can't be too similar to your other personal information.</li>
                                <li>Your password must contain at least 8 characters.</li>
                                <li>Your password can't be a commonly used password.</li>
                                <li>Your password can't be entirely numeric.</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="id_new_password2" class="form-label">Confirm New Password</label>
                        <input type="password" name="new_password2" class="form-control {% if form.new_password2.errors %}is-invalid{% endif %}" id="id_new_password2" required>
                        {% if form.new_password2.errors %}
                        <div class="invalid-feedback">
                            {{ form.new_password2.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Change Password</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
