<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Debug Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h2>Chart Debug Test</h2>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Global Sentiment Analysis</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="globalSentimentChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Client Experience Trend</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="clientImpactTrendChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <h4>Debug Information</h4>
            <div id="debug-info"></div>
        </div>

        <div class="mt-4">
            <h4>Fix Summary</h4>
            <div class="alert alert-success">
                <h5>✅ Charts Display Issue - RESOLVED</h5>
                <p><strong>Root Cause:</strong> Template conditional <code>{% if client_metrics_summary %}</code> was preventing charts from rendering when <code>client_metrics_summary</code> was empty.</p>
                <p><strong>Solution:</strong> Changed conditional to <code>{% if sentiment_analysis_data or client_metrics_summary %}</code> to use either data source.</p>
                <p><strong>Result:</strong> Charts now render using the available <code>sentiment_analysis_data</code> which contains 12 clients with sentiment metrics.</p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const debugInfo = document.getElementById('debug-info');

            function log(message) {
                console.log(message);
                debugInfo.innerHTML += '<p>' + message + '</p>';
            }

            log('Dashboard JavaScript loaded');
            log('Chart.js available: ' + (typeof Chart !== 'undefined'));

            // Check if canvas elements exist
            const globalSentimentCanvas = document.getElementById('globalSentimentChart');
            const clientImpactCanvas = document.getElementById('clientImpactTrendChart');
            log('Global sentiment canvas: ' + (globalSentimentCanvas ? 'Found' : 'Not found'));
            log('Client impact canvas: ' + (clientImpactCanvas ? 'Found' : 'Not found'));

            // Test data (same structure as from Django backend)
            const sentimentData = {
                'Valdes ELIZABEHT': {
                    current_sentiment: -0.31184363157416767,
                    trend: 'declining',
                    severity: 'medium',
                    last_update: '2025-05-30',
                    tickets: 13,
                    resolution_time: 2.15,
                    data_points: 3
                },
                'Sonja TAGHIPOUR': {
                    current_sentiment: -0.30115102864801885,
                    trend: 'stable',
                    severity: 'medium',
                    last_update: '2025-05-30',
                    tickets: 5,
                    resolution_time: 0.0,
                    data_points: 1
                },
                'Ana Maria Galic': {
                    current_sentiment: -0.24637378697904447,
                    trend: 'improving',
                    severity: 'medium',
                    last_update: '2025-05-30',
                    tickets: 12,
                    resolution_time: 0.08,
                    data_points: 3
                },
                'christina HUEBNER': {
                    current_sentiment: -0.19219518452882767,
                    trend: 'stable',
                    severity: 'low',
                    last_update: '2025-05-30',
                    tickets: 5,
                    resolution_time: 0.0,
                    data_points: 1
                },
                'Wallmann GEROLD': {
                    current_sentiment: -0.184376038176318,
                    trend: 'declining',
                    severity: 'low',
                    last_update: '2025-05-30',
                    tickets: 45,
                    resolution_time: 0.96,
                    data_points: 3
                },
                'Stefan RIBISCH': {
                    current_sentiment: -0.1246395396689574,
                    trend: 'improving',
                    severity: 'low',
                    last_update: '2025-05-30',
                    tickets: 3,
                    resolution_time: 0.0,
                    data_points: 4
                },
                'Bertram Schon': {
                    current_sentiment: -0.11638852953910828,
                    trend: 'improving',
                    severity: 'low',
                    last_update: '2025-05-30',
                    tickets: 3,
                    resolution_time: 3.33,
                    data_points: 5
                }
            };

            function createGlobalSentimentChart() {
                log('Creating Global Sentiment Chart...');

                if (!globalSentimentCanvas) {
                    log('ERROR: Global sentiment canvas not found!');
                    return;
                }

                try {
                    const ctx = globalSentimentCanvas.getContext('2d');
                    log('Global sentiment context: ' + (ctx ? 'OK' : 'Failed'));

                    // Convert to array and sort by sentiment (most negative first)
                    const clientSentimentArray = Object.entries(sentimentData).map(([client, data]) => {
                        return {
                            client: client,
                            sentiment: data.current_sentiment,
                            trend: data.trend,
                            severity: data.severity,
                            lastUpdate: data.last_update,
                            tickets: data.tickets,
                            resolutionTime: data.resolution_time,
                            dataPoints: data.data_points
                        };
                    });

                    // Sort by sentiment (most negative first) and take top 7
                    const sortedClients = clientSentimentArray.sort((a, b) => a.sentiment - b.sentiment);
                    const topNegativeClients = sortedClients.slice(0, 7);

                    // Prepare chart data
                    const labels = topNegativeClients.map(client => client.client);
                    const sentimentValues = topNegativeClients.map(client => client.sentiment);

                    log('Chart data prepared: ' + labels.length + ' clients');

                    // Create background colors based on severity
                    const backgroundColors = topNegativeClients.map(client => {
                        switch(client.severity) {
                            case 'critical': return 'rgba(220, 53, 69, 0.8)';
                            case 'high': return 'rgba(255, 99, 132, 0.8)';
                            case 'medium': return 'rgba(255, 159, 64, 0.8)';
                            case 'low': return 'rgba(255, 205, 86, 0.8)';
                            default: return 'rgba(75, 192, 192, 0.8)';
                        }
                    });

                    // Create the chart
                    const chart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: 'Sentiment Score',
                                data: sentimentValues,
                                backgroundColor: backgroundColors,
                                borderWidth: 2,
                                borderRadius: 4,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            animation: {
                                duration: 2000,
                                easing: 'easeInOutQuart'
                            },
                            scales: {
                                y: {
                                    beginAtZero: false,
                                    min: Math.min(...sentimentValues) - 0.1,
                                    max: 0.1,
                                    title: {
                                        display: true,
                                        text: 'Sentiment Score'
                                    }
                                }
                            }
                        }
                    });

                    log('Global sentiment chart created successfully!');

                } catch (error) {
                    log('ERROR creating global sentiment chart: ' + error.message);
                    console.error('Full error:', error);
                }
            }

            function createClientImpactTrendChart() {
                log('Creating Client Impact Trend Chart...');

                if (!clientImpactCanvas) {
                    log('ERROR: Client impact canvas not found!');
                    return;
                }

                try {
                    const ctx = clientImpactCanvas.getContext('2d');
                    log('Client impact context: ' + (ctx ? 'OK' : 'Failed'));

                    // Sample data
                    const labels = ['Jan 15', 'Jan 20', 'Jan 25', 'Jan 30', 'Feb 5', 'Feb 10'];
                    const data = [65, 70, 75, 68, 72, 69];

                    const chart = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: 'Customer Experience Score (%)',
                                data: data,
                                backgroundColor: 'rgba(227, 25, 55, 0.1)',
                                borderColor: 'rgba(227, 25, 55, 1)',
                                borderWidth: 2,
                                tension: 0.3,
                                fill: true
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: false,
                                    min: 0,
                                    max: 100,
                                    title: {
                                        display: true,
                                        text: 'Customer Experience Score (%)'
                                    }
                                }
                            }
                        }
                    });

                    log('Client impact trend chart created successfully!');

                } catch (error) {
                    log('ERROR creating client impact trend chart: ' + error.message);
                    console.error('Full error:', error);
                }
            }

            // Initialize charts
            log('Initializing charts...');
            createGlobalSentimentChart();
            createClientImpactTrendChart();
            log('Chart initialization complete.');
        });
    </script>
</body>
</html>
