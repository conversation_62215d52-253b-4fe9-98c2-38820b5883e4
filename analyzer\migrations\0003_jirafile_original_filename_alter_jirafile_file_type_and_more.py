# Generated by Django 4.2.7 on 2025-05-06 10:39

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('analyzer', '0002_analysisresult_client_metrics'),
    ]

    operations = [
        migrations.AddField(
            model_name='jirafile',
            name='original_filename',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='jirafile',
            name='file_type',
            field=models.CharField(choices=[('csv', 'CSV'), ('excel', 'Excel')], max_length=10),
        ),
        migrations.AlterField(
            model_name='jirafile',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='jira_files', to=settings.AUTH_USER_MODEL),
        ),
    ]
