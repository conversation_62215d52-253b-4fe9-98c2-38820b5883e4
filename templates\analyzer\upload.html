{% extends 'base.html' %}
{% load static %}

{% block title %}Upload File | Vermeg Analysis{% endblock %}

{% block content %}
<div class="row justify-content-center py-4">
    <div class="col-lg-10">
        <!-- Page header -->
        <div class="page-header mb-5">
            <h1 class="fw-bold">Upload File</h1>
            <p class="text-secondary mb-0">Upload your data file for analysis</p>
        </div>

        <div class="row">
            <div class="col-lg-7 mb-4 mb-lg-0">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body p-4">
                        <h4 class="card-title mb-4"><i class="fas fa-cloud-upload-alt text-primary me-2"></i>File Upload</h4>

                        <form method="post" enctype="multipart/form-data" id="upload-form">
                            {% csrf_token %}

                            <!-- Date Selection Section -->
                            <div class="mb-4 p-4 date-selection-section">
                                <h5 class="mb-3"><i class="fas fa-calendar-alt text-primary me-2"></i>Analysis Date</h5>
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <label for="{{ form.analysis_date.id_for_label }}" class="form-label fw-bold">{{ form.analysis_date.label }}</label>
                                        {{ form.analysis_date }}
                                        {% if form.analysis_date.help_text %}
                                            <div class="form-text">{{ form.analysis_date.help_text }}</div>
                                        {% endif %}
                                        {% if form.analysis_date.errors %}
                                            <div class="text-danger small mt-1">
                                                {{ form.analysis_date.errors }}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center date-icon-container">
                                            <i class="fas fa-filter fa-2x text-primary mb-2"></i>
                                            <p class="small text-secondary mb-0">This date will be used to filter and contextualize your analysis results</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Drag & Drop upload area -->
                            <div class="upload-area mb-4 p-5 text-center border-2 border-dashed rounded d-flex flex-column align-items-center justify-content-center" id="drop-area">
                                <i class="fas fa-file-upload fa-3x text-primary mb-3"></i>
                                <h5 class="mb-2">Drag & Drop Files Here</h5>
                                <p class="text-secondary mb-3">or</p>

                                <label for="{{ form.file.id_for_label }}" class="btn btn-primary mb-3">
                                    <i class="fas fa-folder-open me-2"></i>Browse Files
                                </label>

                                <!-- Hidden file input -->
                                <div style="display: none;">
                                    {{ form.file }}
                                </div>

                                <p class="text-secondary small mb-0">Supported formats: .csv, .xlsx</p>
                            </div>

                            <!-- Selected file info -->
                            <div id="file-info" class="mb-4 p-3 bg-light rounded" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file-excel text-success fa-2x me-3"></i>
                                    <div>
                                        <h6 class="mb-1" id="file-name">filename.xlsx</h6>
                                        <span class="text-secondary small" id="file-size">0 KB</span>
                                    </div>
                                    <button type="button" class="btn btn-sm text-danger ms-auto" id="remove-file">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>

                            {% if form.file.errors %}
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                {{ form.file.errors }}
                            </div>
                            {% endif %}

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg" id="submit-btn" disabled>
                                    <i class="fas fa-upload me-2"></i>Upload & Process
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-5">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body p-4">
                        <h4 class="card-title mb-4"><i class="fas fa-info-circle text-primary me-2"></i>File Requirements</h4>

                        <div class="alert alert-info bg-light border-0">
                            <h5 class="fw-bold"><i class="fas fa-list-ul me-2"></i>Required Columns</h5>
                            <p class="mb-2">Your JIRA export should include these columns:</p>
                            <ul class="mb-3">
                                <li>Issue ID or Key</li>
                                <li>Summary or Title</li>
                                <li>Description</li>
                                <li>Status</li>
                                <li>Priority</li>
                                <li>Issue Type</li>
                                <li>Created Date</li>
                            </ul>
                            <p class="small mb-0">Additional columns like Creator, Reporter, or custom fields will enhance the analysis.</p>
                        </div>

                        <div class="mt-4">
                            <h5 class="fw-bold mb-3">How to Export from JIRA</h5>
                            <ol>
                                <li>Go to your JIRA project</li>
                                <li>Create a filter for the issues you want to analyze</li>
                                <li>Click "Export" and select CSV or Excel format</li>
                                <li>Download the file and upload it here</li>
                            </ol>
                        </div>

                        <div class="mt-4">
                            <h5 class="fw-bold mb-3"><i class="fas fa-calendar-check text-primary me-2"></i>Analysis Date</h5>
                            <p class="small text-secondary">The analysis date you select will be used to:</p>
                            <ul class="small text-secondary">
                                <li>Filter data based on the selected timeframe</li>
                                <li>Contextualize analysis results</li>
                                <li>Generate time-based insights</li>
                                <li>Compare trends over different periods</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <a href="{% url 'dashboard' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .border-dashed {
        border-style: dashed !important;
        border-color: #dee2e6;
    }

    .upload-area {
        min-height: 200px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .upload-area:hover, .upload-area.highlight {
        background-color: rgba(0, 86, 179, 0.05);
        border-color: var(--primary) !important;
    }

    /* Date picker styling */
    .date-selection-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 2px solid #e9ecef;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .date-selection-section:hover {
        border-color: var(--primary);
        box-shadow: 0 4px 15px rgba(0, 86, 179, 0.1);
    }

    .date-selection-section input[type="date"] {
        border: 2px solid #dee2e6;
        border-radius: 8px;
        padding: 12px 15px;
        font-size: 16px;
        transition: all 0.3s ease;
    }

    .date-selection-section input[type="date"]:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 0.2rem rgba(0, 86, 179, 0.25);
        outline: none;
    }

    .date-icon-container {
        background: rgba(0, 86, 179, 0.1);
        border-radius: 8px;
        padding: 15px;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const dropArea = document.getElementById('drop-area');
        const fileInput = document.getElementById('{{ form.file.id_for_label }}');
        const fileInfo = document.getElementById('file-info');
        const fileName = document.getElementById('file-name');
        const fileSize = document.getElementById('file-size');
        const removeFile = document.getElementById('remove-file');
        const submitBtn = document.getElementById('submit-btn');

        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });

        // Highlight drop area when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });

        // Handle dropped files
        dropArea.addEventListener('drop', handleDrop, false);

        // Handle file input change
        fileInput.addEventListener('change', handleFiles);

        // Handle date input change
        const dateInput = document.getElementById('{{ form.analysis_date.id_for_label }}');
        dateInput.addEventListener('change', validateForm);

        // Handle remove file
        removeFile.addEventListener('click', function() {
            fileInput.value = '';
            fileInfo.style.display = 'none';
            validateForm();
        });

        // Handle click on drop area
        dropArea.addEventListener('click', function() {
            fileInput.click();
        });

        // Initial form validation
        validateForm();

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        function highlight() {
            dropArea.classList.add('highlight');
        }

        function unhighlight() {
            dropArea.classList.remove('highlight');
        }

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length) {
                fileInput.files = files;
                handleFiles();
            }
        }

        function handleFiles() {
            if (fileInput.files.length) {
                const file = fileInput.files[0];
                displayFileInfo(file);
                validateFile(file);
            }
        }

        function displayFileInfo(file) {
            // Display file name and size
            fileName.textContent = file.name;
            fileSize.textContent = formatBytes(file.size);
            fileInfo.style.display = 'block';

            // Set icon based on file type
            const fileIcon = fileInfo.querySelector('i');
            if (file.name.endsWith('.csv')) {
                fileIcon.className = 'fas fa-file-csv text-success fa-2x me-3';
            } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                fileIcon.className = 'fas fa-file-excel text-success fa-2x me-3';
            } else {
                fileIcon.className = 'fas fa-file text-secondary fa-2x me-3';
            }
        }

        function validateFile(file) {
            const validExtensions = ['csv', 'xlsx', 'xls'];
            const fileExtension = file.name.split('.').pop().toLowerCase();

            if (validExtensions.includes(fileExtension)) {
                validateForm();
            } else {
                alert('Please upload a CSV or Excel file');
                fileInput.value = '';
                fileInfo.style.display = 'none';
                submitBtn.disabled = true;
            }
        }

        function validateForm() {
            const dateInput = document.getElementById('{{ form.analysis_date.id_for_label }}');
            const hasFile = fileInput.files.length > 0;
            const hasDate = dateInput.value !== '';

            submitBtn.disabled = !(hasFile && hasDate);
        }

        function formatBytes(bytes, decimals = 2) {
            if (bytes === 0) return '0 Bytes';

            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        }
    });
</script>
{% endblock %}