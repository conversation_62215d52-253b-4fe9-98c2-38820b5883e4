"""
Test script for BERTopic theme extraction functionality.
This script tests the extract_common_themes function with sample JIRA ticket descriptions.
"""

import pandas as pd
import logging
from analyzer.utils import extract_common_themes, get_default_themes

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_bertopic_theme_extraction():
    """Test the BERTopic-based theme extraction with sample data"""
    
    # Sample JIRA ticket descriptions
    descriptions = [
        "System performance is slow when processing large datasets. Users are experiencing timeouts.",
        "API integration with third-party service is failing with authentication errors.",
        "Database connection drops intermittently during peak usage hours.",
        "Users cannot access the reporting module after the latest update.",
        "Dashboard visualizations are not rendering correctly in Firefox browser.",
        "Need to implement additional security measures for user data protection.",
        "Error occurs when exporting data to Excel format with special characters.",
        "System crashes when multiple users attempt to access the same record simultaneously.",
        "Feature request: Add ability to schedule automated report generation.",
        "UI elements are misaligned on mobile devices with small screens.",
        "Data synchronization between modules is inconsistent and causing data integrity issues.",
        "Login process takes too long and sometimes times out for remote users.",
        "Need to optimize database queries that are causing performance bottlenecks.",
        "Users report that the search functionality is not returning accurate results.",
        "Application fails to load properly when network connectivity is unstable.",
        "Need to implement better error handling for API timeout scenarios.",
        "Configuration settings are not being saved correctly after system restart.",
        "Feature request: Add dark mode option for the user interface.",
        "Documentation needs to be updated to reflect recent changes in the workflow.",
        "System logs are not capturing enough detail to troubleshoot critical errors."
    ]
    
    # Convert to pandas Series to match the function's expected input
    descriptions_series = pd.Series(descriptions)
    
    # Test the theme extraction function
    print("Testing BERTopic theme extraction...")
    try:
        themes = extract_common_themes(descriptions_series)
        
        print("\nExtracted Themes:")
        print("================")
        for theme, score in themes.items():
            print(f"{theme}: {score}")
            
        print("\nTheme extraction successful!")
        return themes
    except Exception as e:
        print(f"Error during theme extraction: {str(e)}")
        print("Using default themes instead.")
        return get_default_themes()

if __name__ == "__main__":
    # Run the test
    test_bertopic_theme_extraction()
