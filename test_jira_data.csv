Issue key,Summary,Issue Type,Status,Priority,Resolution,Assignee,Reporter,Creator,Created,Updated,Last Viewed,Resolved,Description
PROJ-1,System crashes when loading large datasets,Bug,Resolved,<PERSON>,Fixed,<PERSON>,<PERSON>,<PERSON>,2023-01-01,2023-01-15,2023-01-16,2023-01-15,"The system consistently crashes when attempting to load datasets larger than 500MB. This is causing significant performance issues for our enterprise clients. Steps to reproduce: 1. Upload a dataset larger than 500MB 2. Attempt to process the data 3. System crashes with out of memory error"
PROJ-2,Add export to PDF functionality,Feature Request,<PERSON>,Medium,,<PERSON>,<PERSON>,<PERSON>,2023-01-05,2023-01-10,2023-01-11,,"Users have requested the ability to export reports to PDF format. This would enhance the user experience and provide better documentation capabilities. The export should maintain all formatting and include charts/graphs."
PROJ-3,<PERSON>gin page not responsive on mobile devices,Bug,In Progress,<PERSON>,,<PERSON>,<PERSON>,<PERSON>,2023-01-08,2023-01-12,2023-01-13,,"The login page does not display correctly on mobile devices. Elements overlap and the form is unusable on screens smaller than 768px. This is a critical user interface issue affecting mobile users."
PROJ-4,Improve database query performance,Improvement,Open,<PERSON>,,<PERSON>,<PERSON>,<PERSON>,2023-01-10,2023-01-14,2023-01-15,,"Database queries are taking too long to execute, especially for reports with date ranges longer than 6 months. We need to optimize the SQL queries and possibly add indexes to improve performance."
PROJ-5,Security vulnerability in authentication module,Bug,Resolved,Critical,Fixed,Sarah Williams,Security Team,Security Team,2023-01-12,2023-01-18,2023-01-19,2023-01-18,"A critical security vulnerability has been identified in the authentication module. The system is not properly validating JWT tokens, which could allow unauthorized access. This needs immediate attention as it poses a significant security risk."
PROJ-6,Add multi-language support,Feature Request,Open,Low,,Unassigned,Marketing Team,Marketing Team,2023-01-15,2023-01-16,2023-01-17,,"As we expand to international markets, we need to add support for multiple languages. Initially, we should support English, Spanish, French, and German. This includes translating all UI elements and error messages."
PROJ-7,Data visualization charts not rendering correctly,Bug,In Progress,Medium,,John Smith,Jane Doe,Jane Doe,2023-01-18,2023-01-20,2023-01-21,,"The pie charts and bar graphs in the reporting module are not rendering correctly. Colors are inconsistent and labels are being cut off. This affects the data visualization experience for users."
PROJ-8,Implement single sign-on (SSO) integration,Feature Request,Open,High,,Sarah Williams,IT Department,IT Department,2023-01-20,2023-01-22,2023-01-23,,"We need to implement SSO integration with Azure AD to simplify the authentication process for corporate users. This should follow the OAuth 2.0 protocol and support automatic user provisioning."
PROJ-9,System timeout during large report generation,Bug,Open,High,,John Smith,Bob Johnson,Bob Johnson,2023-01-22,2023-01-24,2023-01-25,,"When generating reports with more than 10,000 data points, the system times out after 30 seconds. This is preventing users from creating comprehensive reports. We need to implement asynchronous processing for large reports."
PROJ-10,Enhance error logging and monitoring,Improvement,In Progress,Medium,,DevOps Team,IT Department,IT Department,2023-01-25,2023-01-27,2023-01-28,,"Our current error logging system is inadequate for troubleshooting production issues. We need to enhance the logging to include more context, implement structured logging, and integrate with our monitoring system for better alerting."
