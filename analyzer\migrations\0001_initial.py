# Generated by Django 4.2.7 on 2025-04-29 14:17

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='JiraFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='jira_files/')),
                ('file_type', models.CharField(choices=[('csv', 'CSV'), ('xlsx', 'Excel')], max_length=4)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('processed', models.BooleanField(default=False)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='AnalysisResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('issue_count', models.IntegerField(default=0)),
                ('ticket_types', models.JSONField(default=dict)),
                ('priority_distribution', models.JSONField(default=dict)),
                ('status_distribution', models.JSONField(default=dict)),
                ('common_themes', models.JSONField(default=dict)),
                ('sentiment_analysis', models.JSONField(default=dict)),
                ('jira_file', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analysis_results', to='analyzer.jirafile')),
            ],
        ),
    ]
