from django.urls import path
from django.views.generic import TemplateView
from . import views

urlpatterns = [
    path('', views.home, name='home'),
    path('overview/', views.overview, name='overview'),
    path('dashboard/', views.dashboard, name='dashboard'),
    path('upload/', views.upload_file, name='upload_file'),
    path('process/<int:file_id>/', views.process_file, name='process_file'),
    path('analysis/<int:analysis_id>/', views.view_analysis, name='view_analysis'),
    path('debug-analysis/<int:analysis_id>/', views.debug_analysis, name='debug_analysis'),
    path('download-data/<int:analysis_id>/', views.download_cleaned_data, name='download_cleaned_data'),
    path('regenerate/<int:file_id>/', views.regenerate_analysis, name='regenerate_analysis'),
    path('delete/<int:file_id>/', views.delete_file, name='delete_file'),
    path('ai-agent/', views.ai_agent, name='ai_agent'),
    path('client-overview/', views.client_overview, name='client_overview'),
    path('client-overview/<str:client_name>/', views.client_detail, name='client_detail'),
    path('team-overview/', views.team_overview, name='team_overview'),
    path('test-logo/', TemplateView.as_view(template_name='test_logo.html'), name='test_logo'),

    # Export URLs for Feature 1
    path('export/csv/', views.export_csv, name='export_csv'),
    path('export/excel/', views.export_excel, name='export_excel'),
    path('export/pdf/', views.export_pdf, name='export_pdf'),

    # Notes URLs for Feature 2
    path('client-note/<str:client_name>/', views.get_client_note, name='get_client_note'),
    path('client-note/<str:client_name>/save/', views.save_client_note, name='save_client_note'),
]
