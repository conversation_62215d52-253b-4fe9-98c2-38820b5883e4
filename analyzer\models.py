from django.db import models
from django.contrib.auth.models import User
import json

class JiraFile(models.Model):
    FILE_TYPE_CHOICES = [
        ('csv', 'CSV'),
        ('excel', 'Excel'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='jira_files')
    file = models.FileField(upload_to='jira_files/')
    original_filename = models.CharField(max_length=255, blank=True)
    file_type = models.CharField(max_length=10, choices=FILE_TYPE_CHOICES)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    processed = models.BooleanField(default=False)
    analysis_date = models.DateField(null=True, blank=True, help_text='Date selected for analysis filtering and context')

    def __str__(self):
        return f"{self.original_filename or self.file.name} (Uploaded by {self.user.username})"

    def get_filename(self):
        """Return the original filename if available, otherwise extract from path"""
        if self.original_filename:
            return self.original_filename
        return self.file.name.split('/')[-1]

class AnalysisResult(models.Model):
    jira_file = models.ForeignKey(JiraFile, on_delete=models.CASCADE, related_name='analysis_results')
    issue_count = models.IntegerField(default=0)
    ticket_types = models.JSONField(default=dict)
    priority_distribution = models.JSONField(default=dict)
    status_distribution = models.JSONField(default=dict)
    common_themes = models.JSONField(default=dict)
    sentiment_analysis = models.JSONField(default=dict)
    client_metrics = models.JSONField(default=dict)
    actionable_insights = models.JSONField(default=list)
    theme_visualization = models.JSONField(default=list)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Analysis for {self.jira_file.file.name}"


class ClientNote(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='client_notes')
    client_name = models.CharField(max_length=255)
    note_text = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-updated_at']
        unique_together = ['user', 'client_name']

    def __str__(self):
        return f"Note for {self.client_name} by {self.user.username}"

    def save(self, *args, **kwargs):
        # Ensure fields are properly initialized before saving
        if self.client_metrics is None:
            self.client_metrics = {}
        if self.actionable_insights is None:
            self.actionable_insights = []
        if self.theme_visualization is None:
            self.theme_visualization = []
        super().save(*args, **kwargs)
