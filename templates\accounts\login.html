{% extends 'base.html' %}
{% load static %}

{% block title %}Login | Vermeg Insights{% endblock %}

{% block content %}
<div class="row justify-content-center py-5">
    <div class="col-md-8 col-lg-6 col-xl-5">
        <div class="text-center mb-4">
            <h2 class="fw-bold">Welcome Back</h2>
            <p class="text-secondary">Log in to access your Vermeg Insights dashboard</p>
        </div>

        <div class="card shadow-lg border-0">
            <div class="card-body p-4 p-lg-5">
                <form method="post">
                    {% csrf_token %}

                    <div class="mb-4">
                        <label for="{{ form.username.id_for_label }}" class="form-label">
                            <i class="fas fa-user text-danger me-2"></i>Username
                        </label>
                        <input type="text" name="{{ form.username.name }}" id="{{ form.username.id_for_label }}" class="form-control" required>
                        {% if form.username.errors %}
                        <div class="text-danger mt-2">
                            <i class="fas fa-exclamation-circle me-1"></i>
                            {{ form.username.errors }}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-4">
                        <label for="{{ form.password.id_for_label }}" class="form-label">
                            <i class="fas fa-lock text-danger me-2"></i>Password
                        </label>
                        <input type="password" name="{{ form.password.name }}" id="{{ form.password.id_for_label }}" class="form-control" required>
                        {% if form.password.errors %}
                        <div class="text-danger mt-2">
                            <i class="fas fa-exclamation-circle me-1"></i>
                            {{ form.password.errors }}
                        </div>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2 mt-4">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-sign-in-alt me-2"></i>Login
                        </button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center p-3" style="background-color: var(--gray-light);">
                <p class="mb-0">Don't have an account? <a href="{% url 'register' %}" class="text-decoration-none" style="color: var(--primary);">Register here</a></p>
            </div>
        </div>

        <div class="text-center mt-3">
            <p class="text-secondary small mb-0">
                By logging in, you agree to our <a href="#" class="text-decoration-none">Terms of Service</a> and <a href="#" class="text-decoration-none">Privacy Policy</a>
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% endblock %}