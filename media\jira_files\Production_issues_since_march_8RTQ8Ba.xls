<html xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:x="urn:schemas-microsoft-com:office:excel"
xmlns="http://www.w3.org/TR/REC-html40">
<head>
<title><PERSON>ra</title>
<style type="text/css">
table {
    mso-displayed-decimal-separator:"\.";
    mso-displayed-thousand-separator:"\,";
}
body
{
    margin: 0px;
    font-size: 12px;
    font-family: Arial, sans-serif;
    color:black;
}

</style>
<META HTTP-EQUIV="Content-Type" Content="application/vnd.ms-excel; charset=UTF-8">
<!-- JRA-7598 - ensure fields (e.g. description) occupy only one cell - even fields containing newlines. -->
<!--
Vertical align all cells to the top, in order to align all issue rows of issuetable to the top,
since Excel does not use or save the css files it is hardcoded here.
-->
<style>
@page
{
mso-page-orientation:landscape;
margin:.25in .25in .5in .25in;
mso-header-margin:.5in;
mso-footer-margin:.25in;
mso-footer-data:"&R&P of &N";
mso-horizontal-page-align:center;
mso-vertical-page-align:center;
}

td.issuekey,
td.issuetype,
td.status {
    mso-style-parent: "";
    mso-number-format: \@;
    text-align: left;
}
br
{
    mso-data-placement:same-cell;
}

td
{
    vertical-align: top;
}
</style>

<!--[if gte mso 9]><xml>
<x:ExcelWorkbook>
<x:ExcelWorksheets>
<x:ExcelWorksheet>
<x:Name>general_report</x:Name>
<x:WorksheetOptions>
<x:Print>
<x:ValidPrinterInfo/>
</x:Print>
</x:WorksheetOptions>
</x:ExcelWorksheet>
</x:ExcelWorksheets>
</x:ExcelWorkbook>
</xml><![endif]-->
</head>
<body>

<table border="1">
    <tr bgcolor="#0747a6" height="27">
        <td colspan="15">
            <img src="https://jira.vermeg.com/jira-logo-scaled.png" width="75" height="27" border="0" alt="Jira">
        </td>
    </tr>
    <tr>
        <td colspan="15">
            <a href="https://jira.vermeg.com/issues/?jql=project+%3D+OEKB+AND+issuetype+%3D+Incident+AND+cf%5B12629%5D+%3D+%22Production+Platform%22+AND+created+%3E%3D+2025-03-01">Jira</a>
        </td>
    </tr>
    <tr>
        <td colspan="15">
            Displaying <strong>75</strong> issues at <strong>20/May/25 11:46 AM</strong>.
        </td>
    </tr>
</table>




                            <issuetable-web-component data-content="issues">
                <table id="issuetable"  border="1" cellpadding="3" cellspacing="1" width="100%">
                        <thead>
        <tr class="rowHeader">
            
                                                            <th class="colHeaderLink headerrow-issuetype" data-id="issuetype">
                            Issue Type
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-issuekey" data-id="issuekey">
                            Key
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-issuelinks" data-id="issuelinks">
                            Linked Issues
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-priority" data-id="priority">
                            Priority
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-summary" data-id="summary">
                            Summary
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-status" data-id="status">
                            Status
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-assignee" data-id="assignee">
                            Assignee
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-creator" data-id="creator">
                            Creator
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-created" data-id="created">
                            Created
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-fixVersions" data-id="fixVersions">
                            Fix Version/s
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-customfield_12663" data-id="customfield_12663">
                            Date of First Response
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-resolutiondate" data-id="resolutiondate">
                            Resolved
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-customfield_12503" data-id="customfield_12503">
                            Redeclared
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-customfield_20812" data-id="customfield_20812">
                            Linked issue
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-description" data-id="description">
                            Description
                                                    </th>
                                                                    </tr>
    </thead>
    <tbody>
                    

                <tr id="issuerow1571628" rel="1571628" data-issuekey="OEKB-6890" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6890" href="https://jira.vermeg.com/browse/OEKB-6890">OEKB-6890</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - INTR00163407 - Breakdown Instructions not processed correctly
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 19/May/25 8:49 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="19/May/25 8:59 AM"><time class="livestamp allow-future" datetime="2025-05-19T08:59:48+0100">19/May/25 8:59 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello,
<br/>

<br/>
the client instructions (Breakdown Instruction) are not processed correctly by the system in the two INTR events below.
<br/>

<br/>
INTR00163407 - AT0000A2XML
<br/>

<br/>
Client 236500 =&gt; 17.420.000
<br/>

<br/>
Breakdown Instruction =&gt; 17.430.000
<br/>

<br/>
The instruction exceeds the actual client holding, so it should not be executed.
<br/>

<br/>
However, the instruction has been executed, although this is not visible under &#39;Client Entitlement&#39;.
<br/>

<br/>

<br/>

<br/>
We have the same problem with ISIN AT0000A2XMN6
<br/>

<br/>
INTR00163408 - AT0000A2XMN6
<br/>

<br/>
Client 236500 =&gt; 40.941.000
<br/>

<br/>
Breakdown Instruction =&gt; 40.952.000
<br/>

<br/>
The instruction exceeds the actual client holding, so it should not be executed.
<br/>

<br/>

<br/>

<br/>
Please check this urgently\!
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1571622" rel="1571622" data-issuekey="OEKB-6889" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6889" href="https://jira.vermeg.com/browse/OEKB-6889">OEKB-6889</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - DVCA0003218201 - List Market Entitlement
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 19/May/25 8:10 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="19/May/25 8:39 AM"><time class="livestamp allow-future" datetime="2025-05-19T08:39:41+0100">19/May/25 8:39 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello,
<br/>

<br/>
we have noticed that several market entitlements are displayed for the above DVCA event.
<br/>
The calculated market entitlement of 170,233,686 is correct, but what are the other calculated entitlements (market claims entitlements?)
<br/>
We have now cancelled them, but please urgently check why they are being displayed.
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1571270" rel="1571270" data-issuekey="OEKB-6888" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6888" href="https://jira.vermeg.com/browse/OEKB-6888">OEKB-6888</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - List Client Payment - Different search results
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 16/May/25 12:57 PM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="16/May/25 1:11 PM"><time class="livestamp allow-future" datetime="2025-05-16T13:11:23+0100">16/May/25 1:11 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello,
<br/>

<br/>
if I search in the menu item ‘List Client Payment’ with an ISIN, only results with the status ‘Sent’ are shown.
<br/>
However, if I search using a MainReference, results with other ‘statuses’ are also shown, e.g. with status ‘WaitingSettlementConfirmation’ etc.
<br/>

<br/>

<br/>

<br/>
Please check\!
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dali
  </td>
                    </tr>


                <tr id="issuerow1571133" rel="1571133" data-issuekey="OEKB-6885" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6885" href="https://jira.vermeg.com/browse/OEKB-6885">OEKB-6885</a>
</td>
                                            <td class="issuelinks">                                        OEKB-6798                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                Full Load Position File per aod 15052025 processed after the job for the Entitlment Calculations
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Nikolay TSONKOV
    </td>
                                            <td class="created"> 16/May/25 6:37 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="16/May/25 9:33 AM"><time class="livestamp allow-future" datetime="2025-05-16T09:33:15+0100">16/May/25 9:33 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 16/May/25 9:33 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Dears , 
<br/>

<br/>
last nicht the processing of the  full load position file finished at 3:37 in the morning
<br/>

<br/>

<br/>

<br/>
i will proceed with recovery actions in the following way
<br/>

<br/>
check what was loaded in the system vs content of the full load position file
<br/>

<br/>
do entitlment calculations manually
<br/>

<br/>
run the following jobs in the following order
<br/>

<br/>
* Client Notification Generation
<br/>
* Client notification Generation for Entitlment
<br/>
* Send Notification
<br/>

<br/>
i will keep you updated
<br/>

<br/>
BR, 
<br/>

<br/>
Niki
  </td>
                    </tr>


                <tr id="issuerow1570895" rel="1570895" data-issuekey="OEKB-6884" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6884" href="https://jira.vermeg.com/browse/OEKB-6884">OEKB-6884</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - DVCA0003215836 - incorrect Client Payment Status
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 15/May/25 12:33 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="15/May/25 1:28 PM"><time class="livestamp allow-future" datetime="2025-05-15T13:28:28+0100">15/May/25 1:28 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello,
<br/>

<br/>
four client payments in the DVCA Event mentioned above have the status &#39;WaitingSettlementConfirmation&#39; or &#39;WaitingReleaseConfirmation&#39;, although the instructions are already settled in CCSYS.
<br/>

<br/>
Please check and correct the error so that we can send the confirmations to the clients.
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dali
  </td>
                    </tr>


                <tr id="issuerow1570851" rel="1570851" data-issuekey="OEKB-6883" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6883" href="https://jira.vermeg.com/browse/OEKB-6883">OEKB-6883</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - List Received MT564 CANC - Search Function
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 15/May/25 11:06 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="15/May/25 11:24 AM"><time class="livestamp allow-future" datetime="2025-05-15T11:24:57+0100">15/May/25 11:24 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello, 
<br/>

<br/>
I have noticed that it is not possible to search using an ISIN under the above menu item. Results are only displayed if you click on &#39;Search&#39; without entering an ISIN.
<br/>

<br/>
Please check and adapt.
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1570746" rel="1570746" data-issuekey="OEKB-6882" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6882" href="https://jira.vermeg.com/browse/OEKB-6882">OEKB-6882</a>
</td>
                                            <td class="issuelinks">                                        OEKB-6798                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                Full Position File aod 14.05.2025 processed too late in MegaCor
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Nikolay TSONKOV
    </td>
                                            <td class="created"> 15/May/25 6:42 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="15/May/25 9:25 AM"><time class="livestamp allow-future" datetime="2025-05-15T09:25:09+0100">15/May/25 9:25 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 16/May/25 9:33 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Good Morning the Full Load Position Interface aod 14.05.2025 finished on 15.05.2025 at 02:51
<br/>

<br/>
i will send a meeting for 08:15 so we can do checks together
<br/>

<br/>

<br/>

<br/>
BR, 
<br/>

<br/>
Niki
  </td>
                    </tr>


                <tr id="issuerow1570420" rel="1570420" data-issuekey="OEKB-6879" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6879" href="https://jira.vermeg.com/browse/OEKB-6879">OEKB-6879</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                MEGACOR Flow out -  ERROR
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Lukasz Walczuk
    </td>
                                            <td class="created"> 14/May/25 1:04 PM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="14/May/25 1:14 PM"><time class="livestamp allow-future" datetime="2025-05-14T13:14:31+0100">14/May/25 1:14 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello, 
<br/>

<br/>
we have received two Flow Out Errors. 
<br/>

<br/>

<br/>

<br/>
Please check. 
  </td>
                    </tr>


                <tr id="issuerow1570222" rel="1570222" data-issuekey="OEKB-6878" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6878" href="https://jira.vermeg.com/browse/OEKB-6878">OEKB-6878</a>
</td>
                                            <td class="issuelinks">                                        OEKB-6798                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                Position Delta File #1 per 14052025 not in M12
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Nikolay TSONKOV
    </td>
                                            <td class="created"> 14/May/25 7:53 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="14/May/25 8:17 AM"><time class="livestamp allow-future" datetime="2025-05-14T08:17:12+0100">14/May/25 8:17 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 16/May/25 9:32 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      the position file delta #1 for 14-05-2025 not in the system
<br/>

<br/>
the file is delivered, there is nothing in the failed position
<br/>

<br/>
the device is working
  </td>
                    </tr>


                <tr id="issuerow1569768" rel="1569768" data-issuekey="OEKB-6877" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6877" href="https://jira.vermeg.com/browse/OEKB-6877">OEKB-6877</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - INTR00163325 - Breakdown Instruction
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 13/May/25 8:24 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="13/May/25 8:42 AM"><time class="livestamp allow-future" datetime="2025-05-13T08:42:23+0100">13/May/25 8:42 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello, 
<br/>

<br/>
In the above-mentioned INTR event there is a breakdown instruction (3.000.000 in Status executed) that was not taken into account by the system when calculating the entitlements.
<br/>

<br/>
For this reason, the event status is ‘waiting payment’ instead of ‘executed’.
<br/>

<br/>

<br/>

<br/>
Please check\!
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1569203" rel="1569203" data-issuekey="OEKB-6875" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6875" href="https://jira.vermeg.com/browse/OEKB-6875">OEKB-6875</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                Failed Positions--90 postions not in the system
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Nikolay TSONKOV
    </td>
                                            <td class="created"> 09/May/25 5:31 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="09/May/25 6:02 PM"><time class="livestamp allow-future" datetime="2025-05-09T18:02:36+0100">09/May/25 6:02 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 12/May/25 12:12 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      There is a list with 90 Positions which are not system
<br/>

<br/>
attached is the extraction in the second sheet is shown only unique isins
<br/>

<br/>
the ones marked in red are not matured or having not maturity date
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1569063" rel="1569063" data-issuekey="OEKB-6874" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6874" href="https://jira.vermeg.com/browse/OEKB-6874">OEKB-6874</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - SECU IN View Screen incomplete
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Nadia BOUSSETTA
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 09/May/25 1:08 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="09/May/25 2:18 PM"><time class="livestamp allow-future" datetime="2025-05-09T14:18:45+0100">09/May/25 2:18 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 09/May/25 3:29 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      We noticed that the VIEW screen for SECU IN/Security impact is incomplete (movement characteristics, such as Parity In/out, movement fraction, Immediate Payment and ONGO-field is missing) - see screenshot.  
<br/>

<br/>
Could you please check?
<br/>

<br/>
Thanks, Bert
<br/>

<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1569001" rel="1569001" data-issuekey="OEKB-6873" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6873" href="https://jira.vermeg.com/browse/OEKB-6873">OEKB-6873</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - Failed Received Entitlement MT564 REPE - Error message
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 09/May/25 10:34 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="09/May/25 10:43 AM"><time class="livestamp allow-future" datetime="2025-05-09T10:43:45+0100">09/May/25 10:43 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello,
<br/>

<br/>
we recently received several data records in the ‘Failed Received Entitlement MT564 REPE’ menu item with an error message that we do not understand.
<br/>
Please could you check this and let us know how we should deal with it?
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1568996" rel="1568996" data-issuekey="OEKB-6872" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6872" href="https://jira.vermeg.com/browse/OEKB-6872">OEKB-6872</a>
</td>
                                            <td class="issuelinks">                                        PMG-42770                        </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - Client Payment Waiting FX Rate
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 09/May/25 10:20 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663"></td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello, 
<br/>

<br/>
We have some data records under the &#39;Client Payment Waiting FX Rate&#39; menu that we would like to ignore. Unfortunately, there is no &#39;Ignore&#39; button for this menu item.
<br/>

<br/>
Could you please add an &#39;Ignore&#39; button here?
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1568989" rel="1568989" data-issuekey="OEKB-6871" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6871" href="https://jira.vermeg.com/browse/OEKB-6871">OEKB-6871</a>
</td>
                                            <td class="issuelinks">                                        PMG-42808                        </td>
                                            <td class="priority">           Critical
    </td>
                                            <td class="summary"><p>
                last delta and Full Load Position position file per 07052028 could not be processed 
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Nikolay TSONKOV
    </td>
                                            <td class="created"> 09/May/25 9:55 AM </td>
                                            <td class="fixVersions">                        FullLoad_Hotfix 1660562025            </td>
                                            <td class="customfield_12663">            <span title="09/May/25 10:20 AM"><time class="livestamp allow-future" datetime="2025-05-09T10:20:12+0100">09/May/25 10:20 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 16/May/25 6:20 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      the ticket is to document that 
<br/>

<br/>
on 07.05.2025 it was noticed that the last delta and full load position file could not be processed by the system
<br/>

<br/>

<br/>

<br/>
via support and recovery actions on 07.05.2025 and 08.05.2025 the files could be processed
  </td>
                    </tr>


                <tr id="issuerow1568627" rel="1568627" data-issuekey="OEKB-6870" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6870" href="https://jira.vermeg.com/browse/OEKB-6870">OEKB-6870</a>
</td>
                                            <td class="issuelinks">                                        VEGGO-7942,                                                PMG-42758                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                +++ URGENT +++ M12 PROD - BONU0003219557 - Event Validation failed
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 08/May/25 9:55 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="08/May/25 10:02 AM"><time class="livestamp allow-future" datetime="2025-05-08T10:02:22+0100">08/May/25 10:02 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      BONU0003219557
<br/>
When trying to validate the event, the below error popped up.
<br/>
MT564 IN attached.
<br/>

<br/>
Please check\!
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1568606" rel="1568606" data-issuekey="OEKB-6869" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6869" href="https://jira.vermeg.com/browse/OEKB-6869">OEKB-6869</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                +++ URGENT +++ M12 PROD - MT564 failed in MegaBroker - PALM-04006: Value [RNET] doesn&#39;t exist
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 08/May/25 9:08 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="08/May/25 9:10 AM"><time class="livestamp allow-future" datetime="2025-05-08T09:10:38+0100">08/May/25 9:10 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 08/May/25 9:53 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      MT564 IN (attached) failed in MegaBroker with following error =&gt;
<br/>

<br/>
******FlowIn Identification:*
<br/>

<br/>
# SenderReceiver with identifier (SWIFT ) found related to the device ( BIZ.FIN.TO.CAS )
<br/>

<br/>
# 9 instances of FlowInConfiguration are found for the sender ( SWIFT ).
<br/>

<br/>
# Cannot find any FlowInConfiguration related to the device ( BIZ.FIN.TO.CAS ).
<br/>

<br/>
# FlowIn Configuration with identifier ( MT564_SWIFT_DC_FLOW_IN ) found for the Sender with identifier ( SWIFT ) and the InputDevice with identifier ( BIZ.FIN.TO.CAS ) using the preparsing grammar with identifier ( Swift_Header ).
<br/>

<br/>
# No Domain Configuration found related to the flowIn (120927535)
<br/>

<br/>
******Single Flow Record Type Config Identification:*
<br/>

<br/>
# There is one matching element of type RecordTypeConfiguration ( 1181567002 ) with the pre-parsing result \{block4.SeqA.F23G.Function=NEWM}
<br/>

<br/>
******Single Flow Parsing and Mapping:*
<br/>
*Operation fails due to the following exception:PALM-04016: The following error occurred in the Mapping \[MT564_DataCA_DC_Mapping_In], while trying to map the key \[option] using the Mapping Formula / Rule \[block4.SeqE]: PALM-04016: The following error occurred in the Mapping \[MT564_DataCA_DataOption_DC_Submapping], while trying to map the key \[dataOptionFeature] using the Mapping Formula / Rule \[F22a]: PALM-04016: The following error occurred in the Mapping \[MT564_DataOptionFeature_SubMapping], while trying to map the key \[optionFeaturesIndicator] using the Mapping Formula / Rule \[F22F.OPTF.Indicator]: PALM-04006: Value \[RNET] doesn&#39;t exist in any pair in this enumeration converter \[OptionFeatureEnum_Converter]...*
<br/>

<br/>

<br/>

<br/>
Obviously, there is an issue with 
<br/>
:22F::OPTF//RNET (maybe also with :22F::OPTF//RGRS).
<br/>

<br/>
Could you please check, we need this feed in M12\!
<br/>

<br/>
Thanks, Bert
<br/>

<br/>

<br/>

<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1568242" rel="1568242" data-issuekey="OEKB-6866" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6866" href="https://jira.vermeg.com/browse/OEKB-6866">OEKB-6866</a>
</td>
                                            <td class="issuelinks">                                        OEKB-6848                        </td>
                                            <td class="priority">           Critical
    </td>
                                            <td class="summary"><p>
                URGENT -- Full Load Posistion File per 06052025 not fully processed
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Nikolay TSONKOV
    </td>
                                            <td class="created"> 07/May/25 6:41 AM </td>
                                            <td class="fixVersions">                        FullLoad_Hotfix 1660562025            </td>
                                            <td class="customfield_12663">            <span title="07/May/25 6:44 AM"><time class="livestamp allow-future" datetime="2025-05-07T06:44:53+0100">07/May/25 6:44 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 16/May/25 6:21 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Dears, 
<br/>

<br/>
it seems the full load position file was not fully processed 
<br/>

<br/>
i am sending a meeting for 08:15
<br/>

<br/>

<br/>

<br/>
BR, 
<br/>

<br/>
Niki
  </td>
                    </tr>


                <tr id="issuerow1568038" rel="1568038" data-issuekey="OEKB-6865" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6865" href="https://jira.vermeg.com/browse/OEKB-6865">OEKB-6865</a>
</td>
                                            <td class="issuelinks">                                        OEKB-6855                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                DVCA0003219794 is corrupted
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Wallmann GEROLD
    </td>
                                            <td class="created"> 06/May/25 1:44 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="06/May/25 4:52 PM"><time class="livestamp allow-future" datetime="2025-05-06T16:52:46+0100">06/May/25 4:52 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi,
<br/>

<br/>

<br/>

<br/>
Please can you check DVCA0003219794 -
<br/>

<br/>
It is not possible to go to the Global View Screen / View Screen.
<br/>

<br/>
There is the following error alert:
<br/>

<br/>

<br/>

<br/>
Unknown name value \[ACED00057E720018636F6D2E7665726D65672E6D6363612E43414F726967696E00000000000000001200007872000E6A6176612E6C616E672E456E756D000000000000000012000078707400066D616E75616C] for enum class \[com.vermeg.mcca.CAOrigin]
<br/>

<br/>

<br/>

<br/>
Can you please check and repair
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1567880" rel="1567880" data-issuekey="OEKB-6863" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6863" href="https://jira.vermeg.com/browse/OEKB-6863">OEKB-6863</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - List Client Payments - Status
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 06/May/25 9:38 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="06/May/25 9:47 AM"><time class="livestamp allow-future" datetime="2025-05-06T09:47:26+0100">06/May/25 9:47 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello, 
<br/>

<br/>
i have noticed that the &#39;Status&#39; option in the &#39;List Client Payments&#39; menu no longer works correctly.
<br/>

<br/>
The query option now looks different to the one in the M12QAS.
<br/>

<br/>
You will no longer be able to query the various &#39;statuses&#39;.
<br/>

<br/>

<br/>

<br/>
Please check\!
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dali
  </td>
                    </tr>


                <tr id="issuerow1567699" rel="1567699" data-issuekey="OEKB-6862" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6862" href="https://jira.vermeg.com/browse/OEKB-6862">OEKB-6862</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - To-Do-List &quot;Failed Received seev.002&quot; - Ignore does not work
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 05/May/25 3:18 PM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="05/May/25 6:59 PM"><time class="livestamp allow-future" datetime="2025-05-05T18:59:31+0100">05/May/25 6:59 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Ignore of failed seev.002 messages did not work\!
<br/>
Although message “Ignored successfully” popped up, messages are still in the list.
<br/>

<br/>
Please check\!
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1567345" rel="1567345" data-issuekey="OEKB-6858" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6858" href="https://jira.vermeg.com/browse/OEKB-6858">OEKB-6858</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                +++ URGENT +++ M12 PROD - TEND0003218332 - SECU OUT disappeared in View and Update Screen
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 02/May/25 4:03 PM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="02/May/25 4:22 PM"><time class="livestamp allow-future" datetime="2025-05-02T16:22:51+0100">02/May/25 4:22 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      TEND0003218332
<br/>
This event must be updated (deadlines, pay date to be amended).
<br/>
When trying to update the PAYD for SECU OUT, I noticed that SECU OUT  is not available in the update (and also view) screen anymore\!?
<br/>

<br/>
Could you please urgently check and make it available again?
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1567241" rel="1567241" data-issuekey="OEKB-6857" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6857" href="https://jira.vermeg.com/browse/OEKB-6857">OEKB-6857</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                +++ URGENT +++ M12 PROD - PROX0003218001 - event blocked in validation due to error message
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Ana Maria Galic
    </td>
                                            <td class="created"> 02/May/25 10:31 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="02/May/25 10:36 AM"><time class="livestamp allow-future" datetime="2025-05-02T10:36:27+0100">02/May/25 10:36 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 02/May/25 9:36 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi, 
<br/>

<br/>
PROX0003218001 is stuck in validation due to attached error messages.  I used the EDIT function to update this PROX event because a normal update could not be done due to an other error message (OEKB-6694). 
<br/>

<br/>
Can you please check this urgently? This update has to be sent out asap. Attached the latest REPL message received.
<br/>

<br/>
BR Ana 
  </td>
                    </tr>


                <tr id="issuerow1566913" rel="1566913" data-issuekey="OEKB-6855" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6855" href="https://jira.vermeg.com/browse/OEKB-6855">OEKB-6855</a>
</td>
                                            <td class="issuelinks">                                        OEKB-6865                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                Dashboard alert Validate Manual Creation / Updates does not work any longer
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            Wallmann GEROLD
    </td>
                                            <td class="created"> 30/Apr/25 3:05 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="30/Apr/25 3:18 PM"><time class="livestamp allow-future" datetime="2025-04-30T15:18:49+0100">30/Apr/25 3:18 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      When I want to open the Dash Board alerts Validate Manual Creation / Updates there is an error alert - see attachment - 
<br/>

<br/>

<br/>

<br/>
Please check and repair 
  </td>
                    </tr>


                <tr id="issuerow1566863" rel="1566863" data-issuekey="OEKB-6854" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6854" href="https://jira.vermeg.com/browse/OEKB-6854">OEKB-6854</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                --URgent--Client Notification Generation Job in Failed
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Hafedh BEN SASSI
    </td>
                                            <td class="creator">            OEKB - Nikolay TSONKOV
    </td>
                                            <td class="created"> 30/Apr/25 1:05 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="30/Apr/25 1:51 PM"><time class="livestamp allow-future" datetime="2025-04-30T13:51:38+0100">30/Apr/25 1:51 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 30/Apr/25 4:53 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello Vermeg colleagues, 
<br/>

<br/>
today we see the Client Notification Generation failed execution 
<br/>

<br/>
can i manually execute it?
<br/>

<br/>

<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1566595" rel="1566595" data-issuekey="OEKB-6848" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6848" href="https://jira.vermeg.com/browse/OEKB-6848">OEKB-6848</a>
</td>
                                            <td class="issuelinks">                                        OEKB-6866                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                +++ URGENT +++ M12 PROD - No Entitlements calculated for Client 250500
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 30/Apr/25 9:17 AM </td>
                                            <td class="fixVersions">                        FullLoad_Hotfix 1660562025            </td>
                                            <td class="customfield_12663">            <span title="30/Apr/25 9:35 AM"><time class="livestamp allow-future" datetime="2025-04-30T09:35:53+0100">30/Apr/25 9:35 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 16/May/25 6:21 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi,
<br/>
we noticed for several events (DVCA0003217731, DVCA0003218924, LIQU0003219880) that no entitlements have been calculated yesterday for client *250500* (other entitlements for these MainRefs were calcuated, if any), although there are eligible positions in CCSYS (but none in M12, see screenshots).
<br/>

<br/>
However, this doesn’t refer to all eligible holdings with Ref. 29/4 for this client - some entitlements have been calculated accordingly for 250500\!
<br/>

<br/>
Could you please urgently\!
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1566068" rel="1566068" data-issuekey="OEKB-6847" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6847" href="https://jira.vermeg.com/browse/OEKB-6847">OEKB-6847</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Critical
    </td>
                                            <td class="summary"><p>
                +++ URGENT +++ M12 PROD +++ 2 seev.004 not in MegaCor
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 29/Apr/25 9:37 AM </td>
                                            <td class="fixVersions">                        MR2_2025            </td>
                                            <td class="customfield_12663">            <span title="29/Apr/25 9:44 AM"><time class="livestamp allow-future" datetime="2025-04-29T09:44:00+0100">29/Apr/25 9:44 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 08/May/25 6:48 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Today, we again received two seev.004 meeting instructions (attached) which do not reach MegaCor.
<br/>

<br/>
In MegaBroker, both are in status “Valid Flow” (screenshot).
<br/>
In both instructions, Rights Holder Details seem to be complete (incl. LEI).
<br/>

<br/>
We need these instructions immediately in MegaCor to submit them to the custodian.
<br/>

<br/>
Please check urgently\!
<br/>
Thanks, Bert
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1565149" rel="1565149" data-issuekey="OEKB-6845" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6845" href="https://jira.vermeg.com/browse/OEKB-6845">OEKB-6845</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                +++ URGENT +++ M12 PROD - PROX0003217725 - two seev.004 did not reach Megacor
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Ana Maria Galic
    </td>
                                            <td class="created"> 25/Apr/25 1:03 PM </td>
                                            <td class="fixVersions">                        MR2_2025            </td>
                                            <td class="customfield_12663">            <span title="25/Apr/25 1:13 PM"><time class="livestamp allow-future" datetime="2025-04-25T13:13:43+0100">25/Apr/25 1:13 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 08/May/25 6:49 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi, 
<br/>

<br/>
today we received 7 instructions for PROX event PROX0003217725. 5 of the instructions reached Megacor but 2 of them didn’t although they are valid in Megabroker.
<br/>

<br/>
I also tried to replay one of the missing instructions in Megabroker but the instruction wasn’t sent to Megacor either.
<br/>

<br/>
Can you please urgently check? We need those two instructions asap in Megacor as we have to forward them to the custodian TODAY till 17:00.
<br/>

<br/>
Attached the two flow in’s that didn’t reach Megacor. 
<br/>

<br/>
BR Ana
  </td>
                    </tr>


                <tr id="issuerow1564805" rel="1564805" data-issuekey="OEKB-6842" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6842" href="https://jira.vermeg.com/browse/OEKB-6842">OEKB-6842</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                +++ URGENT +++  M12 Megacor and M12 Megabroker NOT AVAILABLE +++
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Canceled&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Canceled&lt;/span&gt;">Canceled</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Ana Maria Galic
    </td>
                                            <td class="created"> 24/Apr/25 4:08 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="24/Apr/25 4:10 PM"><time class="livestamp allow-future" datetime="2025-04-24T16:10:22+0100">24/Apr/25 4:10 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello, 
<br/>

<br/>
Megacor and Megabroker PROD are not available since approximately 15 minutes\!
<br/>

<br/>
Please urgently check\!\!
<br/>

<br/>
Thanks
<br/>
BR Ana
  </td>
                    </tr>


                <tr id="issuerow1564625" rel="1564625" data-issuekey="OEKB-6840" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6840" href="https://jira.vermeg.com/browse/OEKB-6840">OEKB-6840</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - seev.004 OUT - &lt;IssrLabl&gt; in wrong order
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 24/Apr/25 11:08 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="24/Apr/25 2:07 PM"><time class="livestamp allow-future" datetime="2025-04-24T14:07:59+0100">24/Apr/25 2:07 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      PROX0003218000
<br/>
We received seev.004 (attached) including &lt;IssrLabl&gt; numbers in correct order, see excerpt =&gt;
<br/>

<br/>
&lt;VoteDtls&gt;
<br/>
&lt;VoteInstrForAgndRsltn&gt;
<br/>
&lt;VotePerAgndRsltn&gt;
<br/>
&lt;VoteInstr&gt;
<br/>
*&lt;IssrLabl&gt;2&lt;/IssrLabl&gt;*
<br/>
&lt;For&gt;
<br/>
&lt;Qty&gt;
<br/>
&lt;Unit&gt;1196&lt;/Unit&gt;
<br/>
&lt;/Qty&gt;
<br/>
&lt;/For&gt;
<br/>
&lt;/VoteInstr&gt;
<br/>
&lt;VoteInstr&gt;
<br/>
*&lt;IssrLabl&gt;3&lt;/IssrLabl&gt;*
<br/>
&lt;For&gt;
<br/>
&lt;Qty&gt;
<br/>
&lt;Unit&gt;1196&lt;/Unit&gt;
<br/>
&lt;/Qty&gt;
<br/>
&lt;/For&gt;
<br/>
&lt;/VoteInstr&gt;
<br/>
&lt;VoteInstr&gt;
<br/>
*&lt;IssrLabl&gt;4&lt;/IssrLabl&gt;*
<br/>
&lt;For&gt;
<br/>
&lt;Qty&gt;
<br/>
&lt;Unit&gt;1196&lt;/Unit&gt;
<br/>
&lt;/Qty&gt;
<br/>
&lt;/For&gt;
<br/>
&lt;/VoteInstr&gt;
<br/>
&lt;VoteInstr&gt;
<br/>
*&lt;IssrLabl&gt;5&lt;/IssrLabl&gt;*
<br/>
&lt;For&gt;
<br/>
&lt;Qty&gt;
<br/>
&lt;Unit&gt;1196&lt;/Unit&gt;
<br/>
&lt;/Qty&gt;
<br/>
&lt;/For&gt;
<br/>
&lt;/VoteInstr&gt;
<br/>

<br/>
After market instruction (seev.004 OUT attached), we noticed that &lt;IssrLabl&gt; is in a wrong order, see excerpt =&gt;
<br/>

<br/>
&lt;VoteDtls&gt;
<br/>
&lt;VoteInstrForAgndRsltn&gt;
<br/>
&lt;VotePerAgndRsltn&gt;
<br/>
&lt;VoteInstr&gt;
<br/>
*&lt;IssrLabl&gt;8&lt;/IssrLabl&gt;*
<br/>
&lt;Agnst&gt;
<br/>
&lt;Qty&gt;
<br/>
&lt;Unit&gt;1196&lt;/Unit&gt;
<br/>
&lt;/Qty&gt;
<br/>
&lt;/Agnst&gt;
<br/>
&lt;/VoteInstr&gt;
<br/>
&lt;VoteInstr&gt;
<br/>
*&lt;IssrLabl&gt;6&lt;/IssrLabl&gt;*
<br/>
&lt;For&gt;
<br/>
&lt;Qty&gt;
<br/>
&lt;Unit&gt;1196&lt;/Unit&gt;
<br/>
&lt;/Qty&gt;
<br/>
&lt;/For&gt;
<br/>
&lt;/VoteInstr&gt;
<br/>
&lt;VoteInstr&gt;
<br/>
*&lt;IssrLabl&gt;3&lt;/IssrLabl&gt;*
<br/>
&lt;For&gt;
<br/>
&lt;Qty&gt;
<br/>
&lt;Unit&gt;1196&lt;/Unit&gt;
<br/>
&lt;/Qty&gt;
<br/>
&lt;/For&gt;
<br/>
&lt;/VoteInstr&gt;
<br/>
&lt;VoteInstr&gt;
<br/>
*&lt;IssrLabl&gt;5&lt;/IssrLabl&gt;*
<br/>
&lt;For&gt;
<br/>
&lt;Qty&gt;
<br/>
&lt;Unit&gt;1196&lt;/Unit&gt;
<br/>
&lt;/Qty&gt;
<br/>
&lt;/For&gt;
<br/>
&lt;/VoteInstr&gt;
<br/>

<br/>
Could you please check?
<br/>
Correct order should be considered.
<br/>

<br/>
Thanks, Bert
<br/>

  </td>
                    </tr>


                <tr id="issuerow1564615" rel="1564615" data-issuekey="OEKB-6839" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6839" href="https://jira.vermeg.com/browse/OEKB-6839">OEKB-6839</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                +++ URGENT +++ M12 PROD - List Event - Search by CA Code doesn&#39;t work any longer
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 24/Apr/25 10:56 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="28/Apr/25 9:37 AM"><time class="livestamp allow-future" datetime="2025-04-28T09:37:48+0100">28/Apr/25 9:37 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 28/Apr/25 11:30 AM </td>
                                            <td class="customfield_12503">    YES
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi,
<br/>
we noticed that (suddenly?\!) it is not possible to search by CA Code under *“List Events”*, which is very uncomfortable.
<br/>

<br/>
Same issue when trying to search by CA Code under 
<br/>
”*List Received MT564/Seev.031/Seev.035/Seev.039”*
<br/>
”*List Received MT566/ Seev.036/ Seev.037”*
<br/>

<br/>
Could you please check?
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1564558" rel="1564558" data-issuekey="OEKB-6838" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6838" href="https://jira.vermeg.com/browse/OEKB-6838">OEKB-6838</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - PROX0003219467 - event blocked in validation due to error message
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            OEKB - Ana Maria Galic
    </td>
                                            <td class="created"> 24/Apr/25 9:55 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="24/Apr/25 4:43 PM"><time class="livestamp allow-future" datetime="2025-04-24T16:43:40+0100">24/Apr/25 4:43 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 30/Apr/25 6:05 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi, 
<br/>

<br/>
PROX event PROX0003219467 is stuck in validation process due to attached error.
<br/>
We did a workaround by duplicating the event and the validation was possible. So for this event PROX0003219467 the error message has to be removed so that we can close it.
<br/>

<br/>
Can you please check?
<br/>

<br/>
Thanks
<br/>
BR Ana
  </td>
                    </tr>


                <tr id="issuerow1564480" rel="1564480" data-issuekey="OEKB-6837" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6837" href="https://jira.vermeg.com/browse/OEKB-6837">OEKB-6837</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - SPLR0003216322 - MT564 REPL failed in MegaBroker
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Nadia BOUSSETTA
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 24/Apr/25 8:16 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="24/Apr/25 8:45 AM"><time class="livestamp allow-future" datetime="2025-04-24T08:45:38+0100">24/Apr/25 8:45 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 24/Apr/25 9:57 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      SPLR0003216322
<br/>
MT564 REPL (attached) was received, but failed in MegaBroker with error =&gt;
<br/>

<br/>
*PALM-01371: The value (SPLR) for the field (Ca Code) for the entity (*[*com.vermeg.mcca.CA*|http://com.vermeg.mcca.CA]*) of code (SPLR0003216322) is not valid, possible values are: (\[ACCU, ACTV, ATTI, BIDS, BMET, BONU, BPUT, BRUP, CAPD, CAPG, CAPI, CERT, CHAN, CLSA, CMET, CONS, CONV, COOP, CREV, DECR, DETI, DFLT, DLST, DRAW, DRCA, DRIP, DSCL, DTCH, DVCA, DVOP, DVSC, DVSE, EXOF, EXOP, EXRI, EXTM, EXWA, INCR, INFO, INTR, LIQU, MCAL, MEET, MRGR, NOOF, ODLT, OMET, OTHR, PARI, PCAL, PDEF, PINK, PLAC, PPMT, PRED, PRII, PRIO, PROX, REDM, REDO, REMK, RHDI, RHTS, SHDS, SHPR, SOFF, SPLF, SUPR, SUSP, TEND, TNDP, TREC, WRTH, WTRC, XMET])*
<br/>

<br/>
Could you please check?
<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1563887" rel="1563887" data-issuekey="OEKB-6832" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6832" href="https://jira.vermeg.com/browse/OEKB-6832">OEKB-6832</a>
</td>
                                            <td class="issuelinks">                                        VEGGO-7857                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - PROX0003216471 - Validation of update failed
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 22/Apr/25 3:22 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="22/Apr/25 3:35 PM"><time class="livestamp allow-future" datetime="2025-04-22T15:35:06+0100">22/Apr/25 3:35 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      PROX0003216471
<br/>
seev.001 REPL (attached) was received.
<br/>
When trying to validate (time 22/04/2025, around 16.18), the below error popped up.
<br/>

<br/>
Could you please check?
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1563638" rel="1563638" data-issuekey="OEKB-6830" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6830" href="https://jira.vermeg.com/browse/OEKB-6830">OEKB-6830</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                +++ URGENT +++ M12 PROD - EXRI0003218596 - Default Instruction LAPS rejected although freeze was performed successfully
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 22/Apr/25 8:08 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="22/Apr/25 8:35 AM"><time class="livestamp allow-future" datetime="2025-04-22T08:35:21+0100">22/Apr/25 8:35 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 23/Apr/25 10:48 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      EXRI0003218596
<br/>
Default Instruction (LAPS) was generated by the system, freeze was performed successfully in T2S, see screenshot.
<br/>

<br/>
However, the instruction is in status “Rejected” in M12.
<br/>
When checking the freeze instruction status notifications, I noticed that a freeze cancellation was generated by the system, but failed with error “Cannot invoke &quot;\[B.clone()&quot; because &quot;content&quot; is null”\!?
<br/>
See all notifications below.
<br/>

<br/>
Could you please check?
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1562538" rel="1562538" data-issuekey="OEKB-6829" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6829" href="https://jira.vermeg.com/browse/OEKB-6829">OEKB-6829</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                Unknown error alert
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Wallmann GEROLD
    </td>
                                            <td class="created"> 18/Apr/25 9:36 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="18/Apr/25 9:42 AM"><time class="livestamp allow-future" datetime="2025-04-18T09:42:21+0100">18/Apr/25 9:42 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 18/Apr/25 12:11 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello
<br/>

<br/>

<br/>

<br/>
We have recieved this error alert on 17. April 2025 19:33
<br/>

<br/>
See attachment
  </td>
                    </tr>


                <tr id="issuerow1562247" rel="1562247" data-issuekey="OEKB-6827" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6827" href="https://jira.vermeg.com/browse/OEKB-6827">OEKB-6827</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - PROX0003217007 - reject of update not possible
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Ana Maria Galic
    </td>
                                            <td class="created"> 17/Apr/25 12:43 PM </td>
                                            <td class="fixVersions">                        Production Hotfix 23/04/2025            </td>
                                            <td class="customfield_12663">            <span title="17/Apr/25 12:50 PM"><time class="livestamp allow-future" datetime="2025-04-17T12:50:17+0100">17/Apr/25 12:50 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 21/Apr/25 3:56 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi, 
<br/>

<br/>
I need to reject this update but when I click on “compare draft” following error message appears.
<br/>
Attached you will find the REPL message.
<br/>

<br/>
Please check this issue. If you need logs - I clicked on “compare draft” at 13:42. 
<br/>

<br/>
Thanks
<br/>
BR Ana 
  </td>
                    </tr>


                <tr id="issuerow1561785" rel="1561785" data-issuekey="OEKB-6819" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6819" href="https://jira.vermeg.com/browse/OEKB-6819">OEKB-6819</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - several CHAN events - flow in feeds failed
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Ana Maria Galic
    </td>
                                            <td class="created"> 16/Apr/25 11:46 AM </td>
                                            <td class="fixVersions">                        Production Hotfix 23/04/2025            </td>
                                            <td class="customfield_12663">            <span title="16/Apr/25 12:12 PM"><time class="livestamp allow-future" datetime="2025-04-16T12:12:53+0100">16/Apr/25 12:12 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 22/Apr/25 1:59 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi, 
<br/>

<br/>
we noticed that several CHAN flow ins from 3i and WDBO failed due to following error description:
<br/>

<br/>

<br/>
*“PALM-011503: Value with pk (\{id=730025919}) and type (com.vermeg.services.announcementDC.MCDataCleansingInput) does not exist any more.”*
<br/>

<br/>

<br/>
All those feeds landed in “Repair Received Feed” in M12.
<br/>

<br/>
Attached the 3 flow ins from 3i and the one from ZWP.
<br/>

<br/>
Can you please check what this error is about and moreover how to repair those feeds?
<br/>

<br/>
Thanks
<br/>
BR Ana
  </td>
                    </tr>


                <tr id="issuerow1561176" rel="1561176" data-issuekey="OEKB-6814" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6814" href="https://jira.vermeg.com/browse/OEKB-6814">OEKB-6814</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                +++ URGENT +++ M12 PROD - PROX0003219111 - seev.001 OUT - &lt;PrxyChc&gt; incorrect
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 15/Apr/25 10:34 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="15/Apr/25 10:41 AM"><time class="livestamp allow-future" datetime="2025-04-15T10:41:35+0100">15/Apr/25 10:41 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 15/Apr/25 11:16 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      PROX0003219111
<br/>
seev.001 OUT (attached) contains incorrect tags within &lt;PrxyChc&gt; sequence, see screenshots.
<br/>

<br/>
In seev.001 IN (attached), &lt;PrxyChc&gt; is populated as follows:
<br/>

<br/>
_&lt;PrxyChc&gt;_
<br/>
     _&lt;Prxy&gt;_
<br/>
          _&lt;AuthrsdPrxy&gt;_
<br/>
               _&lt;PrxyTp&gt;CHRM&lt;/PrxyTp&gt;_
<br/>
          _&lt;/AuthrsdPrxy&gt;_
<br/>
          _&lt;AuthrsdPrxy&gt;_
<br/>
               _&lt;PrxyTp&gt;NEPR&lt;/PrxyTp&gt;_
<br/>
          _&lt;/AuthrsdPrxy&gt;_
<br/>
     _&lt;/Prxy&gt;_
<br/>
_&lt;/PrxyChc&gt;_
<br/>

<br/>
Could you please check and adapt the mapping?
<br/>

<br/>
Thanks, Bert
<br/>

  </td>
                    </tr>


                <tr id="issuerow1559580" rel="1559580" data-issuekey="OEKB-6799" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6799" href="https://jira.vermeg.com/browse/OEKB-6799">OEKB-6799</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                Rate Value is wrongfully 0 if even if not present in Redaout Files. 
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Lukasz Walczuk
    </td>
                                            <td class="created"> 10/Apr/25 8:13 AM </td>
                                            <td class="fixVersions">                        Production Hotfix 23/04/2025            </td>
                                            <td class="customfield_12663">            <span title="10/Apr/25 11:34 AM"><time class="livestamp allow-future" datetime="2025-04-10T11:34:25+0100">10/Apr/25 11:34 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 21/Apr/25 3:57 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hallo Emna, 
<br/>

<br/>
do we have a Rule in place that we put a default value 0, in the Rate field, when RateValue or  Next Coupon Rate  is not present?
<br/>

<br/>
If yes, please deactivate it. 
<br/>

<br/>

<br/>

<br/>

<br/>

<br/>
If there is no “Rate Value” or  “Next Coupon Rate” present, leave it blank. 
<br/>

<br/>
Either no interest rate in 3i - then there must not be 0 in the Rate Value or Next Coupon Rate field in the referentials
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1559452" rel="1559452" data-issuekey="OEKB-6796" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6796" href="https://jira.vermeg.com/browse/OEKB-6796">OEKB-6796</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - MT564 OUT - QCAS CODE
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Hafedh BEN SASSI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 09/Apr/25 2:50 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="09/Apr/25 3:13 PM"><time class="livestamp allow-future" datetime="2025-04-09T15:13:07+0100">09/Apr/25 3:13 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello, 
<br/>

<br/>
we have noticed that in the MT564 NEWM, REPL and REPE messages regarding REDM, MCAL and EXWA events, the code &#39;QCAS&#39; is listed in line 22F although there is only one CASH option.
<br/>

<br/>
22F::OPTF//QCAS
<br/>

<br/>
The code description is as follows:
<br/>

<br/>
*Feature whereby the holder should only instruct a cash amount.*
<br/>

<br/>

<br/>

<br/>
We are of the opinion that this line is incorrect.
<br/>

<br/>
Can you please check\!
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dalibor
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1559364" rel="1559364" data-issuekey="OEKB-6793" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6793" href="https://jira.vermeg.com/browse/OEKB-6793">OEKB-6793</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                Post Reda Go Live failed instruments 
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Lukasz Walczuk
    </td>
                                            <td class="created"> 09/Apr/25 6:38 AM </td>
                                            <td class="fixVersions">                        Production Hotfix 23/04/2025            </td>
                                            <td class="customfield_12663">            <span title="09/Apr/25 8:28 AM"><time class="livestamp allow-future" datetime="2025-04-09T08:28:09+0100">09/Apr/25 8:28 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 22/Apr/25 11:46 AM </td>
                                            <td class="customfield_12503">    YES
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hallo Hafedh, 
<br/>

<br/>

<br/>

<br/>
we have some hybrids, which failed today, 
<br/>

<br/>

<br/>

<br/>
can we make a call to check, what date is missing here?
<br/>

<br/>
BR
<br/>

<br/>
Lukasz
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1559133" rel="1559133" data-issuekey="OEKB-6790" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6790" href="https://jira.vermeg.com/browse/OEKB-6790">OEKB-6790</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - OMET0003218861 - Saving of update failed
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 08/Apr/25 12:46 PM </td>
                                            <td class="fixVersions">                        Production Hotfix 23/04/2025            </td>
                                            <td class="customfield_12663">            <span title="08/Apr/25 2:11 PM"><time class="livestamp allow-future" datetime="2025-04-08T14:11:32+0100">08/Apr/25 2:11 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 22/Apr/25 1:55 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      OMET0003218861
<br/>
I updated the event by removing PROX and NOAC Option and added an outgoing comment.
<br/>

<br/>
When trying to save the update, the below error. 
<br/>

<br/>
Could you please check?
<br/>
Thanks, Bert
<br/>

<br/>

<br/>

<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1558568" rel="1558568" data-issuekey="OEKB-6788" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6788" href="https://jira.vermeg.com/browse/OEKB-6788">OEKB-6788</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - SHDS0003218608 - generation of notifications failed 
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Ana Maria Galic
    </td>
                                            <td class="created"> 07/Apr/25 8:31 AM </td>
                                            <td class="fixVersions">                        Hotfix 08/04/2025            </td>
                                            <td class="customfield_12663">            <span title="07/Apr/25 8:41 AM"><time class="livestamp allow-future" datetime="2025-04-07T08:41:18+0100">07/Apr/25 8:41 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 08/Apr/25 12:09 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi, 
<br/>

<br/>
I activated this SHDS event via “set as reliable” and wanted to generate the notifications as usual to send them out to the clients but it’s not possible. After clicking on “GenerateByCA”, this infomation pops up. Attached the seev.045 received. 
<br/>
Can you please urgently check?
<br/>

<br/>
Thanks
<br/>
BR Ana
  </td>
                    </tr>


                <tr id="issuerow1558358" rel="1558358" data-issuekey="OEKB-6786" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6786" href="https://jira.vermeg.com/browse/OEKB-6786">OEKB-6786</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - AT0000A30FD5 - Error in the Generated Schedule
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 04/Apr/25 3:15 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="04/Apr/25 3:43 PM"><time class="livestamp allow-future" datetime="2025-04-04T15:43:26+0100">04/Apr/25 3:43 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello,
<br/>

<br/>
The above ISIN has a coupon date on 07 April 2025.
<br/>

<br/>
The corresponding INTR event is also created in MC.
<br/>

<br/>
However, the MC has also created an INTR event on 11 April 2025.
<br/>

<br/>
Please check why the referentials here are not correct.
<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1558264" rel="1558264" data-issuekey="OEKB-6785" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6785" href="https://jira.vermeg.com/browse/OEKB-6785">OEKB-6785</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - List Received MT564/MT566 - Search for CA Code failed
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 04/Apr/25 11:47 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="04/Apr/25 12:32 PM"><time class="livestamp allow-future" datetime="2025-04-04T12:32:17+0100">04/Apr/25 12:32 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 28/Apr/25 10:11 AM </td>
                                            <td class="customfield_12503">    YES
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi,
<br/>
after deployment from yesterday, we noticed that we are not able to search for CA Codes anymore (no pop up menu available).
<br/>
This refers to 
<br/>
*List Received MT564/Seev.031/Seev.035/Seev.039*
<br/>
*List Received MT566/ Seev.036/ Seev.037*
<br/>

<br/>
But please check if this issue also occurs in other search screens.
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1558212" rel="1558212" data-issuekey="OEKB-6784" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6784" href="https://jira.vermeg.com/browse/OEKB-6784">OEKB-6784</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - 11 PROX events still in status &quot;Preliminary&quot; although updated and validated
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 04/Apr/25 10:45 AM </td>
                                            <td class="fixVersions">                        Hotfix 08/04/2025            </td>
                                            <td class="customfield_12663">            <span title="04/Apr/25 10:51 AM"><time class="livestamp allow-future" datetime="2025-04-04T10:51:40+0100">04/Apr/25 10:51 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 08/Apr/25 12:10 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi,
<br/>

<br/>
Ana updated 11 PROX events, I validated all of them.
<br/>
However, we noticed that these events are all still in status “Preliminary” (see screenshots).
<br/>

<br/>
Unlike other PROX events, all of them have no Record Dates, maybe this has to do with this issue. 
<br/>
Events must move to “Activated” anyway, after validating.
<br/>

<br/>
Could you please urgently check?
<br/>

<br/>
Thanks, Bert
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1558114" rel="1558114" data-issuekey="OEKB-6783" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6783" href="https://jira.vermeg.com/browse/OEKB-6783">OEKB-6783</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - EXRI - OVER Option - Screen should not include &quot;Is Broker Option&quot;, &quot;Trade Begin&quot;, &quot;Trade End&quot;
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 04/Apr/25 8:40 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663"></td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi,
<br/>
Today I noticed that there are fields in the OVER Option screen (within EXRI event), which are irrelevant, could you please check and skip those?
<br/>

<br/>
See screenshot.
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1557459" rel="1557459" data-issuekey="OEKB-6776" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6776" href="https://jira.vermeg.com/browse/OEKB-6776">OEKB-6776</a>
</td>
                                            <td class="issuelinks">                                        OEKB-6641                        </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - PROX0003216320/ PROX0003216486 - &#39;compare draft&#39; not possible
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Ana Maria Galic
    </td>
                                            <td class="created"> 02/Apr/25 3:21 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="02/Apr/25 3:23 PM"><time class="livestamp allow-future" datetime="2025-04-02T15:23:28+0100">02/Apr/25 3:23 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 04/Apr/25 8:37 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi, 
<br/>

<br/>
for these two prox events we received REPL messages, but I can’t do anything - when I click on ‘compare draft’ this error appears.
<br/>

<br/>
Attached you will find the NEWM and the REPL received for these two events.
<br/>

<br/>
Please check this asap.
<br/>

<br/>
Thanks
<br/>
BR Ana 
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1557413" rel="1557413" data-issuekey="OEKB-6775" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6775" href="https://jira.vermeg.com/browse/OEKB-6775">OEKB-6775</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - BIDS0003217779 - Global View - MT567 OUT not available
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 02/Apr/25 2:10 PM </td>
                                            <td class="fixVersions">                        MR1_2025            </td>
                                            <td class="customfield_12663">            <span title="02/Apr/25 2:21 PM"><time class="livestamp allow-future" datetime="2025-04-02T14:21:25+0100">02/Apr/25 2:21 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 08/Apr/25 12:12 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi,
<br/>
I noticed that MT567 OUT messages are not available in Global View under “MT567/CAIS/Report/Seev.032/Seev.034/Seev.041” (see screenshot 1) although a lot of MT567 were sent for this event and can be found under “List Sent MT567/ Seev.034/ Seev.032/ Seev.041” (see screenshot 2).
<br/>

<br/>
Please check\!
<br/>
Thanks, Bert
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1557403" rel="1557403" data-issuekey="OEKB-6774" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6774" href="https://jira.vermeg.com/browse/OEKB-6774">OEKB-6774</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - BIDS0003217779 - Consolidate ALL did not work accordingly
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 02/Apr/25 1:43 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="02/Apr/25 2:06 PM"><time class="livestamp allow-future" datetime="2025-04-02T14:06:10+0100">02/Apr/25 2:06 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      BIDS0003217779
<br/>
We received 13 client instructions (see screenshot) for CASH option.
<br/>

<br/>
I wanted to consolidate ALL, but this did not work accordingly as 
<br/>
there are now 5 Market Entitlements each for CASH and SECU OUT, instead of 1 for CASH and 1 for SECU OUT (see screenshot).
<br/>

<br/>
Could you please check this behavior?
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1557029" rel="1557029" data-issuekey="OEKB-6768" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6768" href="https://jira.vermeg.com/browse/OEKB-6768">OEKB-6768</a>
</td>
                                            <td class="issuelinks">                                        OEKB-6746                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - AT0WEB2310A6 PCAL Event is missing
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 01/Apr/25 8:20 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="01/Apr/25 8:34 AM"><time class="livestamp allow-future" datetime="2025-04-01T08:34:02+0100">01/Apr/25 8:34 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello,
<br/>

<br/>
here we have a second case in which we are missing the PCAL Event (19.04.2025).
<br/>

<br/>
Please check\!
<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1556946" rel="1556946" data-issuekey="OEKB-6767" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6767" href="https://jira.vermeg.com/browse/OEKB-6767">OEKB-6767</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - PROX0003216319 - validation of update not possible
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Ana Maria Galic
    </td>
                                            <td class="created"> 31/Mar/25 12:29 PM </td>
                                            <td class="fixVersions">                        Production Hotfix 23/04/2025            </td>
                                            <td class="customfield_12663">            <span title="31/Mar/25 12:34 PM"><time class="livestamp allow-future" datetime="2025-03-31T12:34:26+0100">31/Mar/25 12:34 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 08/Apr/25 12:16 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi, 
<br/>

<br/>
on friday I updated this PROX event - I validated the REPL message because the meeting resolutions changed and I could save the update. The order of the meeting resolutions is still not correct (ticket OEKB-6731).
<br/>
Anyway, Bert could not validate my update because an error appears. 
<br/>

<br/>
Attached the received REPL message.
<br/>

<br/>
Thanks
<br/>
BR Ana 
  </td>
                    </tr>


                <tr id="issuerow1556945" rel="1556945" data-issuekey="OEKB-6766" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6766" href="https://jira.vermeg.com/browse/OEKB-6766">OEKB-6766</a>
</td>
                                            <td class="issuelinks">                                        PMG-42508                        </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - PROX0003216653 - validation of REPL not possible
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Nadia BOUSSETTA
    </td>
                                            <td class="creator">            OEKB - Ana Maria Galic
    </td>
                                            <td class="created"> 31/Mar/25 12:20 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="31/Mar/25 12:28 PM"><time class="livestamp allow-future" datetime="2025-03-31T12:28:00+0100">31/Mar/25 12:28 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi, 
<br/>

<br/>
I wanted to validate the REPL message as some meeting resolutions were added but I received this error message.
<br/>
Attached the REPL message we received.
<br/>

<br/>
Can you please check?
<br/>
Thanks
<br/>

<br/>
BR Ana
  </td>
                    </tr>


                <tr id="issuerow1556562" rel="1556562" data-issuekey="OEKB-6761" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6761" href="https://jira.vermeg.com/browse/OEKB-6761">OEKB-6761</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - PRED0003209274 Update not possible - URGENT
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 28/Mar/25 8:16 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="28/Mar/25 8:33 AM"><time class="livestamp allow-future" datetime="2025-03-28T08:33:22+0000">28/Mar/25 8:33 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 18/Apr/25 2:08 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello,
<br/>

<br/>
The above-mentioned PRED event cannot be updated.
<br/>

<br/>
Please check\!
<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1556167" rel="1556167" data-issuekey="OEKB-6749" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6749" href="https://jira.vermeg.com/browse/OEKB-6749">OEKB-6749</a>
</td>
                                            <td class="issuelinks">                                        PMG-42462                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - AT0000A16WK4 - Error in the Generated Schedule
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 27/Mar/25 8:33 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="27/Mar/25 8:39 AM"><time class="livestamp allow-future" datetime="2025-03-27T08:39:13+0000">27/Mar/25 8:39 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello, 
<br/>

<br/>
The above ISIN has a coupon date of 28 March 2025.
<br/>

<br/>
The interest period is from 28 March 2024 to 27 March 2025, which is 360 days.
<br/>

<br/>
Although the interest period displayed is correct, Megacor calculates 362 days. This results in an incorrect interest rate.
<br/>

<br/>
Please check why the referentials here are not correct.
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1555874" rel="1555874" data-issuekey="OEKB-6746" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6746" href="https://jira.vermeg.com/browse/OEKB-6746">OEKB-6746</a>
</td>
                                            <td class="issuelinks">                                        PMG-42461,                                                OEKB-6768                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - AT0000A073W3 PCAL Event is missing
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 26/Mar/25 12:27 PM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="26/Mar/25 12:32 PM"><time class="livestamp allow-future" datetime="2025-03-26T12:32:26+0000">26/Mar/25 12:32 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello, 
<br/>

<br/>
A PCAL event (15.04.2025) for the above ISIN is missing in M12.
<br/>

<br/>
Please check\!
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1554983" rel="1554983" data-issuekey="OEKB-6744" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6744" href="https://jira.vermeg.com/browse/OEKB-6744">OEKB-6744</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                +++ URGENT +++ M12 PROD - seev.001 IN received with Error in MegaBroker
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 25/Mar/25 6:58 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="25/Mar/25 7:21 AM"><time class="livestamp allow-future" datetime="2025-03-25T07:21:54+0000">25/Mar/25 7:21 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 18/Apr/25 3:25 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi,
<br/>
could you please urgently check the attached seev.001 IN which was received in MegaBroker with error description “*Could not receive Message”*?\! =&gt;
<br/>

<br/>
******FlowIn Identification:*
<br/>

<br/>
# SenderReceiver with identifier (SWIFT20022_SRD ) found related to the device ( 3i.iso20022.to.mb )
<br/>

<br/>
# 31 instances of FlowInConfiguration are found for the sender ( SWIFT20022_SRD ).
<br/>

<br/>
# Cannot find any FlowInConfiguration related to the device ( 3i.iso20022.to.mb ).
<br/>

<br/>
# FlowIn Configuration with identifier ( SEEV.001.001.11_DATACA_FLOWIN ) found for the Sender with identifier ( SWIFT20022_SRD ) and the InputDevice with identifier ( 3i.iso20022.to.mb ) using the preparsing grammar with identifier ( HEAD20022 ).
<br/>

<br/>
# No Domain Configuration found related to the flowIn (120768704)
<br/>

<br/>
******Single Flow Record Type Config Identification:*
<br/>

<br/>
# The configuration ( SEEV.001.001.11_DATACA_FLOWIN ) has one RecordTypeConfiguration ( 1182917002 )
<br/>

<br/>
******Receivers Determination Failed:*
<br/>
*Could not receive Message.*
<br/>

<br/>
Thanks, Bert
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1554559" rel="1554559" data-issuekey="OEKB-6742" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6742" href="https://jira.vermeg.com/browse/OEKB-6742">OEKB-6742</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Critical
    </td>
                                            <td class="summary"><p>
                M12 PROD - AT000B078746 - INTR0003212604 - Incorrect payment URGENT
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 24/Mar/25 8:02 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="24/Mar/25 8:11 AM"><time class="livestamp allow-future" datetime="2025-03-24T08:11:22+0000">24/Mar/25 8:11 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 11/Apr/25 5:02 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello, 
<br/>

<br/>
the above INTR event was incorrectly executed on 21/03/2025.
<br/>

<br/>
Client entitlements have not been correctly calculated.
<br/>

<br/>
Client 247200 =&gt; Eligible 286.800.000
<br/>

<br/>
                        =&gt; Calculated 261.800.000
<br/>

<br/>
Client 250500 =&gt; Eligible 102.500.000
<br/>

<br/>
                       =&gt; Calculated 127.500.000
<br/>

<br/>
The holdings displayed under &#39;Client Eligible Position&#39; are correct&#39;.
<br/>

<br/>

<br/>

<br/>
Please check urgently\!
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dalibor
<br/>

<br/>

<br/>

<br/>

<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1554091" rel="1554091" data-issuekey="OEKB-6741" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6741" href="https://jira.vermeg.com/browse/OEKB-6741">OEKB-6741</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - REDM0003212605 - incorrect Client and Market Calculation - URGENT
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 21/Mar/25 10:14 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="21/Mar/25 11:09 AM"><time class="livestamp allow-future" datetime="2025-03-21T11:09:01+0000">21/Mar/25 11:09 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello, 
<br/>

<br/>
the entitlements for the ISIN mentioned above have been calculated incorrectly.
<br/>

<br/>
The redemption rate is 100%. This means that the redemption amount is EUR 1.000.000,00.
<br/>

<br/>
However, only EUR 1.000,00 has been calculated.
<br/>

<br/>
In the Swift messages under &#39;92A&#39; it says that the rate is 0;1. This is not correct.
<br/>

<br/>
Please check urgently as today is payday.
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dali
  </td>
                    </tr>


                <tr id="issuerow1553869" rel="1553869" data-issuekey="OEKB-6739" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6739" href="https://jira.vermeg.com/browse/OEKB-6739">OEKB-6739</a>
</td>
                                            <td class="issuelinks">                                        OEKB-6545,                                                OEKB-6666,                                                OEKB-6705                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - Event automatically closed - INTR00157866
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 20/Mar/25 12:19 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="20/Mar/25 12:33 PM"><time class="livestamp allow-future" datetime="2025-03-20T12:33:35+0000">20/Mar/25 12:33 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 23/Mar/25 2:31 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello,
<br/>

<br/>
the below INTR Event has been closed which is not correct.
<br/>

<br/>
INTR00157866
<br/>

<br/>
Please check\!
<br/>

<br/>
Thank you and BR, Dali
  </td>
                    </tr>


                <tr id="issuerow1553847" rel="1553847" data-issuekey="OEKB-6735" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6735" href="https://jira.vermeg.com/browse/OEKB-6735">OEKB-6735</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - DVCA0003214189 - Event set to &quot;Confirmed&quot;
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 20/Mar/25 10:16 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="20/Mar/25 10:40 AM"><time class="livestamp allow-future" datetime="2025-03-20T10:40:39+0000">20/Mar/25 10:40 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 18/Apr/25 3:23 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello, 
<br/>

<br/>
the above event was set to status ‘confirmed’ and this is not correct.
<br/>

<br/>
The reason for this is probably that the ‘Market Entitlement’ was not calculated (is cancelled manually).
<br/>

<br/>
Please check and change the status to the previous status “activated” “executed”.
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1553830" rel="1553830" data-issuekey="OEKB-6733" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6733" href="https://jira.vermeg.com/browse/OEKB-6733">OEKB-6733</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - No E-Mail-Alert received for Meeting Cancellation (seev.002)
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Nadia BOUSSETTA
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 20/Mar/25 8:20 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="20/Mar/25 8:42 AM"><time class="livestamp allow-future" datetime="2025-03-20T08:42:15+0000">20/Mar/25 8:42 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi,
<br/>
we recently received 3 seev.002 meeting cancellation messages from our custodian, but we noticed that we did not get alerted via E-Mail for 2 of those 3.
<br/>
Only one alert was received (see screenshot) for MainRef PROX0003215277.
<br/>

<br/>
We also noticed that for the other 2, no MainRef is assigned to cancellation message, although in both cases, valid events with corresponding Meeting ID (“Market Reference”) exist.
<br/>

<br/>
Could you please check?
<br/>
Thanks, Bert
<br/>

<br/>

<br/>

<br/>

<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1553561" rel="1553561" data-issuekey="OEKB-6731" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6731" href="https://jira.vermeg.com/browse/OEKB-6731">OEKB-6731</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - PROX0003216820/ PROX0003216821 - order of meeting resolutions not correct
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Nadia BOUSSETTA
    </td>
                                            <td class="creator">            OEKB - Ana Maria Galic
    </td>
                                            <td class="created"> 19/Mar/25 12:21 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="19/Mar/25 12:49 PM"><time class="livestamp allow-future" datetime="2025-03-19T12:49:54+0000">19/Mar/25 12:49 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      We noticed that in some PROX events the meeting resolutions are mapped completely incorrect in M12 in comparison to the seev.001 received.
<br/>

<br/>
Attached you will find two examples:
<br/>

<br/>
ISIN IT0001031084 - received seev.001 from our custodian and our sent seev.001 to the clients
<br/>
ISIN - ES0113900J37 - received seev.001 from our custodian and our sent seev.001 to the clients
<br/>

<br/>

<br/>
Can you please check this behaviour? I think it could be related to OEKB-6694 as I could not save these two PROX events and had to do a workaround by duplicating the events (I reveiced the same error message as described in OEKB-6694). 
<br/>

<br/>

<br/>
Thanks
<br/>
BR Ana
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1553510" rel="1553510" data-issuekey="OEKB-6730" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6730" href="https://jira.vermeg.com/browse/OEKB-6730">OEKB-6730</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - Client Payment Status - WaitingCashBookingConfirmation - DVCA0003216059
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Canceled&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Canceled&lt;/span&gt;">Canceled</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 19/Mar/25 10:28 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="19/Mar/25 10:54 AM"><time class="livestamp allow-future" datetime="2025-03-19T10:54:41+0000">19/Mar/25 10:54 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello,
<br/>

<br/>
In event DVCA0003216059 there is a claim booking in USD (14,07) for 240000 (credit) but the status is “WaitingCashBookingConfirmation”.
<br/>

<br/>
Is this the same problem as in ticket “OEKB-6675”?
<br/>

<br/>
Please check\!
<br/>

<br/>
Thank you and BR, Dali
  </td>
                    </tr>


                <tr id="issuerow1552763" rel="1552763" data-issuekey="OEKB-6721" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6721" href="https://jira.vermeg.com/browse/OEKB-6721">OEKB-6721</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - CCSYS Instruction with Status &quot;Rejected&quot;
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 17/Mar/25 2:00 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="17/Mar/25 2:58 PM"><time class="livestamp allow-future" datetime="2025-03-17T14:58:45+0000">17/Mar/25 2:58 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 19/Mar/25 3:13 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello, 
<br/>

<br/>
A claim has been created in the event below.
<br/>

<br/>
AT0000A146T3 - DVCA0003215162
<br/>

<br/>
However, the instruction in CCSYS was rejected with the reason “ The Receiving Securities Account does not exist in T2S”
<br/>

<br/>
I have found several claims that have been rejected with this reason.
<br/>

<br/>
It concerns Cross CSD Instructions.
<br/>

<br/>

<br/>

<br/>
Please check\!
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dalibor
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1550987" rel="1550987" data-issuekey="OEKB-6713" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6713" href="https://jira.vermeg.com/browse/OEKB-6713">OEKB-6713</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - - Error in the Generated Schedule AT0000A30FD5
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Nadia BOUSSETTA
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 13/Mar/25 4:59 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="14/Mar/25 8:17 AM"><time class="livestamp allow-future" datetime="2025-03-14T08:17:20+0000">14/Mar/25 8:17 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello,
<br/>

<br/>
Please check why the &#39;Loan Schedule&#39; was not created correctly for the AT0000A30FD5.
<br/>

<br/>
The next coupon date should be the 7th of April 2025 and the last coupon date was the 7th of October 2024 but M12 calculate the coupon from 11.04.2024.
<br/>

<br/>
Coupons from 2024 are not available in the Loan Schedule.
<br/>

<br/>
Thank you and BR, Dali
  </td>
                    </tr>


                <tr id="issuerow1550960" rel="1550960" data-issuekey="OEKB-6712" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6712" href="https://jira.vermeg.com/browse/OEKB-6712">OEKB-6712</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - AT000B015078 - Error in the Generated Schedule
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 13/Mar/25 3:58 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="13/Mar/25 4:07 PM"><time class="livestamp allow-future" datetime="2025-03-13T16:07:18+0000">13/Mar/25 4:07 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello,
<br/>

<br/>
Could you please check why the &#39;Loan Schedule&#39; for the AT000B015078 has not been created correctly?
<br/>

<br/>
The next coupon date should be the 7th of April 2025 and the last coupon date was the 6th of January 2025 but M12 calculates the coupon from 08.01.2025 to 07.04.2025
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dalibor
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1550286" rel="1550286" data-issuekey="OEKB-6708" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6708" href="https://jira.vermeg.com/browse/OEKB-6708">OEKB-6708</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12-I5: - MCAL0003211223 - MT564 WITH Message not sent to 3i
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 13/Mar/25 11:20 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="13/Mar/25 11:27 AM"><time class="livestamp allow-future" datetime="2025-03-13T11:27:04+0000">13/Mar/25 11:27 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello,
<br/>

<br/>
the above event has been withdrawn.
<br/>

<br/>
I have noticed, that the MT564 WITH message was not sent to DEF005 (3i), DEF001 and DEF003.
<br/>

<br/>
Please check\!
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1549121" rel="1549121" data-issuekey="OEKB-6704" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6704" href="https://jira.vermeg.com/browse/OEKB-6704">OEKB-6704</a>
</td>
                                            <td class="issuelinks">                                        PMG-42363                        </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - PLAC0003214814 - EFFD must be a critical field / Automatic update must not remove the outgoing comment
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 12/Mar/25 11:12 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="12/Mar/25 11:22 AM"><time class="livestamp allow-future" datetime="2025-03-12T11:22:45+0000">12/Mar/25 11:22 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      PLAC0003214814
<br/>
was set up without Effective Date due to 564 IN (attached).
<br/>
Today, we receive 564 REPL (attached) including the Effective Date.
<br/>
However, the event was automatically updated - obviously, Effective Date is not defined as critical field.
<br/>
Also, I noticed that the outgoing comment was removed in the event data due to the automatic update.
<br/>
See attached 564 NEWM OUT including the outgoing comment and 564 REPL OUT which was automatically sent without outgoing comment, which is confusing for the client.
<br/>

<br/>
Please make sure that
<br/>
*Effective Date is defined as critical field*
<br/>
and that
<br/>
the *Outgoing Comment must never be removed by an automatic update*
<br/>

<br/>
Thanks, Bert
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1549117" rel="1549117" data-issuekey="OEKB-6703" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6703" href="https://jira.vermeg.com/browse/OEKB-6703">OEKB-6703</a>
</td>
                                            <td class="issuelinks">                                        PMG-42396                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - MT566 from 3i has not arrived in M12 - URGENT
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 12/Mar/25 11:06 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="12/Mar/25 11:10 AM"><time class="livestamp allow-future" datetime="2025-03-12T11:10:11+0000">12/Mar/25 11:10 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello, 
<br/>

<br/>
MT566 message from 3i for AT0000A2HB37 - INTR0003211756  has not arrived in M12.
<br/>

<br/>
Please check urgently\!
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dalibor
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1547909" rel="1547909" data-issuekey="OEKB-6698" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6698" href="https://jira.vermeg.com/browse/OEKB-6698">OEKB-6698</a>
</td>
                                            <td class="issuelinks">                                        OEKB-6641                        </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - PROX0003216361 - Validate/Reject of manual update not possible 
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Nadia BOUSSETTA
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 10/Mar/25 8:10 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="10/Mar/25 8:25 AM"><time class="livestamp allow-future" datetime="2025-03-10T08:25:46+0000">10/Mar/25 8:25 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 10/Mar/25 10:07 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      PROX0003216361
<br/>
I update the event manually (added ProcessingTextForNextIntermediary and ANOU). When trying to validate, the below error popped up.
<br/>

<br/>
As workaround, I duplicated the event, validation was successfull.
<br/>

<br/>
Hence, PROX0003216361 should be immediately closed after validation/rejection of the update.
<br/>

<br/>
Please let me know when the event is “unblocked” and ready to be closed.
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1546511" rel="1546511" data-issuekey="OEKB-6695" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6695" href="https://jira.vermeg.com/browse/OEKB-6695">OEKB-6695</a>
</td>
                                            <td class="issuelinks">                                        PMG-42343,                                                OEKB-6543                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - Reconciliation Mismatch with Market CP -  DVCA0003214713 - URGENT
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 06/Mar/25 10:41 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="06/Mar/25 11:20 AM"><time class="livestamp allow-future" datetime="2025-03-06T11:20:18+0000">06/Mar/25 11:20 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello, 
<br/>

<br/>
The market payment for the above dividend payment was successful. However, no client booking has been made.
<br/>

<br/>
There is a Reconciliation Mismatch with Market Cash Payment and I believe that this is again due to the cent difference between collection letter and the market entitlement.
<br/>

<br/>

<br/>

<br/>
Please check urgently\!
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1546459" rel="1546459" data-issuekey="OEKB-6694" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6694" href="https://jira.vermeg.com/browse/OEKB-6694">OEKB-6694</a>
</td>
                                            <td class="issuelinks">                                        VEGGO-7857                        </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 PROD - PROX0003216162 - saving of event not possible
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            OEKB - Ana Maria Galic
    </td>
                                            <td class="created"> 06/Mar/25 9:35 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="06/Mar/25 9:47 AM"><time class="livestamp allow-future" datetime="2025-03-06T09:47:55+0000">06/Mar/25 9:47 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi, 
<br/>

<br/>
PROX0003216162 - I updated the announcement date and the client deadline time in this PROX event and wanted to save the update but then this error popped up. Can you please check what this error is about? 
<br/>
Attached the newest REPL message from this PROX event.
<br/>

<br/>
Thank you
<br/>
BR Ana 
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1545705" rel="1545705" data-issuekey="OEKB-6692" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6692" href="https://jira.vermeg.com/browse/OEKB-6692">OEKB-6692</a>
</td>
                                            <td class="issuelinks">                                        PMG-42352                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 PROD - AT0000A1PHY1 - PCAL0003204291 Update not found
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Dalibor VIDIC
    </td>
                                            <td class="created"> 04/Mar/25 3:58 PM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="04/Mar/25 4:03 PM"><time class="livestamp allow-future" datetime="2025-03-04T16:03:53+0000">04/Mar/25 4:03 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hello, 
<br/>

<br/>
I have updated the above ISIN and it has been validated.
<br/>

<br/>
At the same time Gerold did an update. This update could not be found.
<br/>

<br/>
It is also possible to update the event again. However, these updates cannot be found under the menu item Event Validation =&gt; Validate/Reject Manual Creation/Updates.
<br/>

<br/>

<br/>

<br/>
Please check\!
<br/>

<br/>

<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>
                </tbody>
    </table>
    </issuetable-web-component>
    <div class="end-of-stable-message"></div>
            <table border="1" >
<tr>
    <td bgcolor="#dddddd" colspan="15"><font size="1">
        Generated at Tue May 20 10:46:47 UTC 2025 by Emna EL MEKKI using Jira 9.4.25#940025-sha1:6cf935f91739b43d1216a637e5c998e86376bd2e.
    </font></td>
</tr>
</table>

</body>
</html>