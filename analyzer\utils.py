import logging
import sys
import pandas as pd
import numpy as np
import re
import nltk
import torch
import json
import os
import tempfile
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from nltk.stem import WordNetLemmatizer
from transformers import <PERSON>Tokenizer, AutoModelForSequenceClassification
from .html_excel_parser import is_html_excel, parse_html_excel, convert_html_to_excel

# Configure logging to write to a file and print to console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('nlp_pipeline.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

def read_all_sheets_from_excel(filepath):
    """
    Reads all sheets from an Excel file into a dictionary of pandas DataFrames.

    Args:
        filepath (str): The path to the Excel file.

    Returns:
        dict: A dictionary where keys are sheet names and values are pandas DataFrames.
    """
    # First check if this is an HTML-based Excel file
    if is_html_excel(filepath):
        logging.info(f"Detected HTML-based Excel file: {filepath}")

        # Try to parse the HTML Excel file
        html_df = parse_html_excel(filepath)
        if html_df is not None:
            # For HTML Excel files, we typically only have one sheet
            return {'Sheet1': html_df}
        else:
            # Try to convert the HTML Excel file to a real Excel file
            logging.info("Attempting to convert HTML Excel file to real Excel")
            converted_file = convert_html_to_excel(filepath)
            if converted_file:
                try:
                    result = pd.read_excel(converted_file, sheet_name=None)
                    logging.info(f"Successfully loaded converted Excel file with {len(result)} sheets")
                    # Clean up temp file
                    os.remove(converted_file)
                    return result
                except Exception as conv_error:
                    logging.error(f"Failed to load converted Excel file: {str(conv_error)}")

    # Try to determine the appropriate engine based on file extension
    file_ext = filepath.lower().split('.')[-1]

    try:
        # First try with auto-detection
        return pd.read_excel(filepath, sheet_name=None)
    except Exception as e:
        logging.warning(f"Auto-detection failed for {filepath}: {str(e)}")

        # Try with explicit engines
        if file_ext == 'xls':
            try:
                # First try standard xlrd approach
                return pd.read_excel(filepath, sheet_name=None, engine='xlrd')
            except Exception as e2:
                logging.warning(f"Standard xlrd engine failed for {filepath}: {str(e2)}")

                # Try direct xlrd approach for Excel 97-2003 files
                try:
                    import xlrd
                    logging.info("Trying direct xlrd approach for Excel 97-2003 file")

                    # Open the workbook directly with xlrd
                    workbook = xlrd.open_workbook(filepath)
                    sheet_names = workbook.sheet_names()

                    # Create a dictionary to hold DataFrames for each sheet
                    result = {}

                    for sheet_name in sheet_names:
                        sheet = workbook.sheet_by_name(sheet_name)

                        # Convert xlrd sheet to pandas DataFrame
                        data = []
                        for row_idx in range(sheet.nrows):
                            row = sheet.row_values(row_idx)
                            data.append(row)

                        # Create DataFrame with first row as header
                        if data:
                            headers = data[0]
                            df = pd.DataFrame(data[1:], columns=headers)
                            result[sheet_name] = df
                        else:
                            result[sheet_name] = pd.DataFrame()

                    logging.info(f"Successfully loaded Excel 97-2003 file with direct xlrd approach")
                    return result
                except Exception as e_direct:
                    logging.error(f"Direct xlrd approach failed for {filepath}: {str(e_direct)}")

        # Try with openpyxl for xlsx files
        try:
            return pd.read_excel(filepath, sheet_name=None, engine='openpyxl')
        except Exception as e3:
            logging.error(f"openpyxl engine failed for {filepath}: {str(e3)}")

        # Try CSV as a last resort
        try:
            logging.info("Trying to load file as CSV as last resort")
            csv_df = pd.read_csv(filepath, sep=None, engine='python')  # Auto-detect separator
            logging.info(f"Successfully loaded file as CSV with {len(csv_df)} rows")
            return {'Sheet1': csv_df}
        except Exception as csv_error:
            logging.error(f"Failed to load file as CSV: {str(csv_error)}")

        # If all attempts fail, raise the last exception
        raise Exception(f"Could not read Excel file {filepath}: Excel file format cannot be determined")

def clean_description(text):
    # Initialize lemmatizer if not already initialized
    global lemmatizer
    if 'lemmatizer' not in globals() or lemmatizer is None:
        lemmatizer = WordNetLemmatizer()

    # Remove non-alphabetic characters and numbers
    text = re.sub(r'[^a-zA-Z\s]', '', str(text))
    text = re.sub(r'http\S+|@\S+|\d+', '', text)

    # Handle contractions - combine into a single regex operation
    contractions = {
        r"n\'t": " not", r"\'re": " are", r"\'s": " is",
        r"\'d": " would", r"\'ll": " will", r"\'t": " not",
        r"\'ve": " have", r"\'m": " am"
    }
    for pattern, replacement in contractions.items():
        text = re.sub(pattern, replacement, text)

    # Tokenize text
    tokens = word_tokenize(text.lower())

    # Remove stopwords
    stop_words = set(stopwords.words('english'))
    tokens = [word for word in tokens if word not in stop_words]

    # Lemmatize tokens
    tokens = [lemmatizer.lemmatize(word) for word in tokens]

    return ' '.join(tokens)

def calculate_sentiment(text):
    try:
        # Initialize tokenizer and model if not already initialized
        global tokenizer, model
        if 'tokenizer' not in globals() or tokenizer is None:
            tokenizer = AutoTokenizer.from_pretrained("ProsusAI/finbert")
        if 'model' not in globals() or model is None:
            model = AutoModelForSequenceClassification.from_pretrained("ProsusAI/finbert")

        # FinBERT sentiment analysis
        inputs = tokenizer(text, return_tensors="pt", truncation=True, padding=True, max_length=512)
        with torch.no_grad():
            outputs = model(**inputs)

        # Get probabilities
        probs = torch.nn.functional.softmax(outputs.logits, dim=-1)

        # Convert to -1 to 1 scale (positive - negative)
        sentiment = probs[0][0].item() - probs[0][1].item()  # positive(0) - negative(1)

        # Domain-specific urgency term adjustments
        urgency_terms = {
            'blocker': -0.0, 'critical': -0.0,
        }

        # Optimize by checking for terms in a single pass through the text
        text_lower = text.lower()
        for term, score in urgency_terms.items():
            if term in text_lower:
                sentiment += score

        # Clamp final value
        return max(min(sentiment, 1.0), -1.0)

    except Exception as e:
        logging.error(f"Sentiment error for text '{text[:30]}...': {str(e)}")
        return 0.0  # Return neutral on error

def process_jira_file(jira_file):
    """
    Process a JIRA export file and perform NLP analysis.

    Args:
        jira_file: A JiraFile model instance

    Returns:
        tuple: (analysis_results dict, client_metrics DataFrame)
    """
    try:
        # Download necessary NLTK data
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
        nltk.download('wordnet', quiet=True)
        nltk.download('omw-1.4', quiet=True)

        # Get file path
        FILEPATH = jira_file.file.path
        logging.info(f"Processing file: {FILEPATH}")

        # First check if this is an HTML-based Excel file
        if is_html_excel(FILEPATH):
            logging.info(f"Detected HTML-based Excel file: {FILEPATH}")

            # Try to parse the HTML Excel file
            html_df = parse_html_excel(FILEPATH)
            if html_df is not None:
                df = html_df
                logging.info(f"Successfully parsed HTML Excel file with {len(df)} rows")
                success = True
            else:
                # Try to convert the HTML Excel file to a real Excel file
                logging.info("Attempting to convert HTML Excel file to real Excel")
                converted_file = convert_html_to_excel(FILEPATH)
                if converted_file:
                    try:
                        df = pd.read_excel(converted_file, header=0)
                        logging.info(f"Successfully loaded converted Excel file with {len(df)} rows")
                        success = True
                        # Clean up temp file
                        os.remove(converted_file)
                    except Exception as conv_error:
                        logging.error(f"Failed to load converted Excel file: {str(conv_error)}")
                        success = False
                else:
                    logging.error("Failed to convert HTML Excel file")
                    success = False
        else:
            # Not an HTML Excel file, try standard Excel approaches
            success = False

            # Try to load the Excel file with different approaches
            try:
                # First try with specific sheet name and auto-detect engine
                df = pd.read_excel(FILEPATH, sheet_name='general_report', header=3)
                logging.info("Successfully loaded 'general_report' sheet")
                success = True
            except Exception as e:
                logging.warning(f"Could not load 'general_report' sheet: {str(e)}")
                try:
                    # Try with explicit engines if auto-detection fails
                    engines = ['openpyxl', 'xlrd']

                    for engine in engines:
                        try:
                            logging.info(f"Trying to load Excel file with engine: {engine}")
                            # Try with ExcelFile first to get sheet names
                            with pd.ExcelFile(FILEPATH, engine=engine) as excel_file:
                                all_sheets = excel_file.sheet_names
                                logging.info(f"Available sheets with {engine} engine: {all_sheets}")

                                if all_sheets:
                                    df = pd.read_excel(excel_file, sheet_name=all_sheets[0], header=3)
                                    logging.info(f"Successfully loaded first sheet: {all_sheets[0]} with {engine} engine")
                                    success = True
                                    break
                                else:
                                    # Try without sheet name
                                    df = pd.read_excel(FILEPATH, header=3, engine=engine)
                                    logging.info(f"Loaded Excel file without specifying sheet name using {engine} engine")
                                    success = True
                                    break
                        except Exception as engine_error:
                            logging.warning(f"Failed to load with {engine} engine: {str(engine_error)}")
                            continue

                    if not success:
                        # If all engines failed, try specialized approaches for .xls files
                        if FILEPATH.lower().endswith('.xls'):
                            # First try the simplest approach for Excel 97-2003 files
                            try:
                                logging.info("Detected .xls file (Excel 97-2003 format), trying simplest approach first")
                                # Try with explicit xlrd engine and no header
                                df = pd.read_excel(
                                    FILEPATH,
                                    engine='xlrd',
                                    header=None  # Read without assuming header position
                                )
                                # If we get here, we successfully loaded the file
                                logging.info("Successfully loaded .xls file with xlrd engine and no header")

                                # Now try to determine the header row
                                # Assume header is in one of the first 5 rows
                                for i in range(5):
                                    if i < len(df):
                                        # Use this row as header
                                        header_row = df.iloc[i]
                                        df = df.iloc[i+1:].reset_index(drop=True)
                                        df.columns = header_row
                                        logging.info(f"Using row {i} as header")
                                        break

                                success = True
                            except Exception as simple_error:
                                logging.warning(f"Simple approach failed: {str(simple_error)}")

                                # Try with xlrd engine specifically for old Excel 97-2003 files
                                try:
                                    logging.info("Trying more complex approach with direct xlrd access")
                                    import xlrd

                                    # Open the workbook directly with xlrd
                                    workbook = xlrd.open_workbook(FILEPATH)
                                    sheet_names = workbook.sheet_names()
                                    logging.info(f"Available sheets in .xls file: {sheet_names}")

                                    if sheet_names:
                                        # Use the first sheet
                                        sheet = workbook.sheet_by_name(sheet_names[0])
                                        # Convert xlrd sheet to pandas DataFrame
                                        data = []
                                        for row_idx in range(sheet.nrows):
                                            if row_idx < 3:  # Skip header rows (assuming header=3)
                                                continue
                                            row = sheet.row_values(row_idx)
                                            data.append(row)

                                        # Create column names from the 4th row (index 3)
                                        if sheet.nrows > 3:
                                            columns = sheet.row_values(3)
                                        else:
                                            columns = [f"Column_{i}" for i in range(sheet.ncols)]

                                        # Create DataFrame
                                        df = pd.DataFrame(data, columns=columns)
                                        logging.info(f"Successfully loaded .xls file using direct xlrd approach")
                                        success = True
                                    else:
                                        logging.error("No sheets found in the .xls file")
                                except Exception as xls_error:
                                    logging.error(f"Failed to load .xls file with direct xlrd approach: {str(xls_error)}")

                        # If all approaches failed, try a generic CSV approach as last resort
                        if not success:
                            try:
                                logging.info("Trying to load file as CSV as last resort")
                                df = pd.read_csv(FILEPATH, sep=None, engine='python')  # Auto-detect separator
                                logging.info(f"Successfully loaded file as CSV with {len(df)} rows")
                                success = True
                            except Exception as csv_error:
                                logging.error(f"Failed to load file as CSV: {str(csv_error)}")

                                # If all approaches failed, raise the exception
                                if not success:
                                    raise Exception("All file reading approaches failed")
                except Exception as e2:
                    logging.error(f"Failed to load Excel file: {str(e2)}")
                    raise Exception(f"Could not read Excel file: {str(e2)}")
            except Exception as e2:
                logging.error(f"Failed to load Excel file: {str(e2)}")
                raise Exception(f"Could not read Excel file: {str(e2)}")

        # Drop specified columns
        cols_to_drop = ["Linked Issues", "Status", "Assignee", "Fix Version/s", "Resolved", "Linked issue"]
        existing_cols_to_drop = [col for col in df.columns if col in cols_to_drop]
        df_cleaned = df.drop(columns=existing_cols_to_drop)

        # Handle Description columns more robustly
        logging.info(f"Columns before processing: {df_cleaned.columns.tolist()}")

        # Check if there are duplicate 'Description' columns
        description_cols = [col for col in df_cleaned.columns if 'description' in col.lower()]
        logging.info(f"Found description columns: {description_cols}")

        if len(description_cols) > 1:
            # More efficient approach - directly filter columns to keep
            columns_to_keep = df_cleaned.columns.tolist()
            # Find the first 'Description' column and remove it
            for i, col in enumerate(columns_to_keep):
                if col.strip() == 'Description':
                    columns_to_keep.pop(i)
                    break

            df_cleaned = df_cleaned[columns_to_keep]
            logging.info(f"Dropped first Description column, remaining columns: {df_cleaned.columns.tolist()}")
        else:
            logging.info("No duplicate Description columns found")

        # Keep only the desired rows
        df_cleaned = df_cleaned.head(360)

        # Skip saving and reloading intermediate file - use the dataframe directly
        df = df_cleaned.copy()

        # Handle Creator column more robustly
        if 'Creator' in df.columns:
            # Clean "Creator" column
            df['Creator'] = df['Creator'].str.replace(r'^.*? - ', '', regex=True).str.strip()
            logging.info("Cleaned Creator column")
        else:
            # Try to find a suitable column for Creator
            possible_creator_cols = ['Reporter', 'Author', 'Created by', 'User']
            creator_col = None

            for col in possible_creator_cols:
                if col in df.columns:
                    creator_col = col
                    break

            if creator_col:
                # Use the found column and rename it
                df['Creator'] = df[creator_col].str.replace(r'^.*? - ', '', regex=True).str.strip()
                logging.info(f"Using '{creator_col}' as Creator column")
            else:
                # Create a default Creator column if none exists
                df['Creator'] = 'Unknown User'
                logging.warning("No Creator column found, using 'Unknown User'")

        # Handle Key column for deduplication
        if 'Key' in df.columns:
            # Remove duplicates based on UNIQUE Key
            df = df.drop_duplicates(subset="Key", keep='first')
            logging.info("Removed duplicates based on Key column")
        else:
            # Try to find a suitable column for deduplication
            possible_key_cols = ['Issue key', 'ID', 'Ticket ID', 'Issue ID']
            key_col = None

            for col in possible_key_cols:
                if col in df.columns:
                    key_col = col
                    break

            if key_col:
                # Use the found column for deduplication
                df = df.drop_duplicates(subset=key_col, keep='first')
                logging.info(f"Removed duplicates based on '{key_col}' column")
            else:
                # If no key column, try to use index or just keep all rows
                logging.warning("No Key column found for deduplication, keeping all rows")

        # Initialize NLP components - only once if not already initialized
        global tokenizer, model, lemmatizer
        if 'tokenizer' not in globals() or tokenizer is None:
            tokenizer = AutoTokenizer.from_pretrained("ProsusAI/finbert")
        if 'model' not in globals() or model is None:
            model = AutoModelForSequenceClassification.from_pretrained("ProsusAI/finbert")
        if 'lemmatizer' not in globals() or lemmatizer is None:
            lemmatizer = WordNetLemmatizer()

        # Apply text cleaning and sentiment analysis
        # Find the description column - try different possible names
        description_col = next((col for col in ['Description.1', 'Description', 'description', 'Summary']
                               if col in df.columns), None)

        if description_col:
            logging.info(f"Using '{description_col}' column for text analysis")
        else:
            # If no description column found, use the first text column as fallback
            text_cols = [col for col in df.columns if df[col].dtype == 'object']
            if text_cols:
                description_col = text_cols[0]
                logging.info(f"No standard description column found, using '{description_col}' instead")
            else:
                # Create a dummy column if no text column exists
                description_col = 'dummy_description'
                df[description_col] = 'No description available'
                logging.warning("No text columns found in the data, using dummy descriptions")

        df['cleaned_description'] = df[description_col].apply(clean_description)
        df['sentiment'] = df['cleaned_description'].apply(calculate_sentiment)

        # Handle date columns more robustly
        # Find Created date column using more efficient approach
        created_col = next((col for col in ['Created', 'Created Date', 'Creation Date', 'Date Created']
                           if col in df.columns), None)

        if created_col is None:
            # If no created column found, create a dummy one with today's date
            created_col = 'Created'
            df[created_col] = pd.Timestamp.now()
            logging.warning("No Created date column found, using current date")
        else:
            # Convert to datetime
            df[created_col] = pd.to_datetime(df[created_col], errors='coerce')
            logging.info(f"Using '{created_col}' as creation date")

        # Find Response date column using more efficient approach
        response_col = next((col for col in ['Date of First Response', 'First Response', 'Response Date', 'Responded']
                            if col in df.columns), None)

        if response_col is None:
            # If no response column found, create a dummy one with today's date
            response_col = 'Date of First Response'
            df[response_col] = df[created_col] + pd.Timedelta(days=3)  # Assume 3 days response time
            logging.warning("No Response date column found, using created date + 3 days")
        else:
            # Convert to datetime
            df[response_col] = pd.to_datetime(df[response_col], errors='coerce')
            logging.info(f"Using '{response_col}' as response date")

        # Calculate resolution time
        df['Resolution Time (Days)'] = (df[response_col] - df[created_col]).dt.days
        df['Resolution Time (Days)'] = df['Resolution Time (Days)'].clip(lower=0)
        df['Resolution Time (Days)'] = df['Resolution Time (Days)'].fillna(0)

        # Calculate temporal decay
        df['days_old'] = (pd.Timestamp.now() - df[created_col]).dt.days
        df['temporal_decay'] = np.exp(-df['days_old'] / 60)

        # --- CALCULATION COMPONENTS ---
        # 1. Sentiment Impact (50% weight)
        df['sentiment_impact'] = ((1 - df['sentiment']) / 2) * 0.50

        # 2. Ticket Impact (20% weight)
        df['decayed_ticket_count'] = df.groupby('Creator')['temporal_decay'].transform('sum')
        max_decayed = df['decayed_ticket_count'].max()
        df['ticket_impact'] = (df['decayed_ticket_count'] / max_decayed) * 0.20

        # 3. Priority Impact (15% weight)
        priority_weights = {
            'Major': 0.13, 'Medium': 0.10, 'Minor': 0.07,
            'Critical': 0.15, 'Blocker': 0.14
        }

        # Find Priority column using more efficient approach
        priority_col = next((col for col in ['Priority', 'Severity', 'Importance', 'Urgency']
                            if col in df.columns), None)

        if priority_col is None:
            # If no priority column found, create a dummy one with medium priority
            priority_col = 'Priority'
            df[priority_col] = 'Medium'
            logging.warning("No Priority column found, using 'Medium' as default")

        # Apply priority weights
        df['priority_impact'] = df[priority_col].astype(str).str.strip().str.title().map(
            lambda x: priority_weights.get(x, 0.10)
        )
        logging.info(f"Using '{priority_col}' for priority impact calculation")

        # 4. Issue Type Impact (15% weight)
        issue_type_weights = {
            'Incident': 0.25, 'Defect': 0.2,
            'Information Request': 0.1, 'Requirement': 0.15
        }

        # Find Issue Type column using more efficient approach
        issue_type_col = next((col for col in ['Issue Type', 'Type', 'Issue Category', 'Category']
                              if col in df.columns), None)

        if issue_type_col is None:
            # If no issue type column found, create a dummy one
            issue_type_col = 'Issue Type'
            df[issue_type_col] = 'Task'
            logging.warning("No Issue Type column found, using 'Task' as default")

        # Apply issue type weights
        df['Issue_Type_impact'] = df[issue_type_col].astype(str).str.strip().map(
            lambda x: issue_type_weights.get(x, 0.10)
        )
        df['Issue_Type_impact'] = (df['Issue_Type_impact'] / 0.25) * 0.15  # Normalize to 15%
        logging.info(f"Using '{issue_type_col}' for issue type impact calculation")

        # Combine components
        df['urgency_score'] = (
            df['sentiment_impact'] +
            df['priority_impact'] +
            df['ticket_impact'] +
            df['Issue_Type_impact']
        ) * 3  # Scale to 0-3

        # Final adjustments
        df['urgency_score'] = ((df['urgency_score'] - 1) / (3 - 1) * 100).round(2).astype(str) + '%'

        # Group by Creator
        # Use the description column we identified earlier for ticket counting
        agg_dict = {
            'sentiment': 'mean',
            'urgency_score': lambda x: x.str.rstrip('%').astype(float).mean(),
            'priority_impact': 'mean',
            'Issue_Type_impact': 'mean',
            'Resolution Time (Days)': 'mean'
        }

        # Add the description column for counting tickets
        agg_dict[description_col] = 'count'

        client_grouped = df.groupby('Creator').agg(agg_dict).reset_index()

        # Rename columns
        rename_dict = {
            'urgency_score': 'Customer_Experience_Score',
            'priority_impact': 'Priority_Impact',
            'Issue_Type_impact': 'Issue_Type_Impact',
            'Resolution Time (Days)': 'Avg_Resolution_Time_Days'
        }

        # Add the description column rename
        rename_dict[description_col] = 'Tickets'

        client_grouped.rename(columns=rename_dict, inplace=True)

        # Reorder columns
        client_grouped = client_grouped[
            ['Creator', 'sentiment', 'Priority_Impact',
             'Issue_Type_Impact', 'Tickets', 'Avg_Resolution_Time_Days', 'Customer_Experience_Score']
        ]

        # Convert 'Customer Experience Score' from percentage to decimal
        client_grouped['Customer_Experience_Score'] = client_grouped['Customer_Experience_Score'] / 100

        # Export results
        client_grouped.to_excel('Jira_NLP_By_Client.xlsx', index=False)

        # Export full data with impact columns
        df.to_csv('cleaned_jira_data_with_impact.csv', index=False)

        # Convert client_grouped DataFrame to dictionary format for the database
        # More efficient approach using DataFrame.to_dict() and dictionary comprehension
        client_metrics_dict = {}
        client_data = client_grouped.set_index('Creator').to_dict('index')

        for creator, metrics in client_data.items():
            # Use numpy_encoder to handle NumPy types properly
            creator_str = str(numpy_encoder(creator))
            client_metrics_dict[creator_str] = {
                'sentiment': numpy_encoder(metrics['sentiment']),
                'Priority_Impact': numpy_encoder(metrics['Priority_Impact']),
                'Issue_Type_Impact': numpy_encoder(metrics['Issue_Type_Impact']),
                'Tickets': numpy_encoder(metrics['Tickets']),
                'Avg_Resolution_Time_Days': numpy_encoder(metrics['Avg_Resolution_Time_Days']),
                'Client_Impact': numpy_encoder(metrics['Customer_Experience_Score']),
                'Customer_Experience_Score': numpy_encoder(metrics['Customer_Experience_Score'])
            }

        # If no client metrics were generated, use sample data as fallback
        if not client_metrics_dict:
            client_metrics_dict = generate_sample_client_metrics()
            logging.warning("No client metrics generated, using sample data")
        else:
            logging.info(f"Generated client metrics for {len(client_metrics_dict)} creators")

        # Prepare analysis results for the database with proper JSON serialization
        analysis_results = {
            'issue_count': numpy_encoder(len(df)),
            'ticket_types': {k: numpy_encoder(v) for k, v in df[issue_type_col].value_counts().to_dict().items()},
            'priority_distribution': {k: numpy_encoder(v) for k, v in df[priority_col].value_counts().to_dict().items()},
            'status_distribution': {},  # Initialize with empty dict
            'sentiment_analysis': {
                'positive': numpy_encoder((df['sentiment'] > 0.3).sum()),
                'neutral': numpy_encoder(((df['sentiment'] >= 0) & (df['sentiment'] <= 0.3)).sum()),
                'negative_low': numpy_encoder(((df['sentiment'] >= -0.2) & (df['sentiment'] < 0)).sum()),
                'negative_medium': numpy_encoder(((df['sentiment'] >= -0.5) & (df['sentiment'] < -0.2)).sum()),
                'negative_high': numpy_encoder((df['sentiment'] < -0.5).sum())
            },
            'client_metrics': client_metrics_dict  # Already processed with numpy_encoder
        }

        # Add status distribution if Status column exists - using more efficient approach
        status_col = next((col for col in ['Status', 'State', 'Ticket Status']
                          if col in df.columns), None)

        if status_col:
            analysis_results['status_distribution'] = {
                k: numpy_encoder(v) for k, v in df[status_col].value_counts().to_dict().items()
            }
            logging.info(f"Using '{status_col}' for status distribution")
        else:
            # Create a default status distribution
            df_len = len(df)
            analysis_results['status_distribution'] = {
                'Open': numpy_encoder(int(df_len * 0.3)),
                'In Progress': numpy_encoder(int(df_len * 0.4)),
                'Resolved': numpy_encoder(int(df_len * 0.3))
            }
            logging.warning("No Status column found, using default status distribution")

        logging.info("Analysis completed successfully")
        return analysis_results, client_grouped

    except Exception as e:
        logging.error(f"Error processing file: {str(e)}")
        raise e

def numpy_encoder(obj):
    """Custom JSON encoder to handle NumPy types"""
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, pd.Series):
        return obj.to_dict()
    return obj

class NumpyJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder class for NumPy types"""
    def default(self, obj):
        return numpy_encoder(obj)



def generate_sample_client_metrics():
    """Generate sample client metrics for testing"""
    return {
        "John Doe": {
            "sentiment": 0.75,
            "Priority_Impact": 0.85,
            "Issue_Type_Impact": 0.65,
            "Tickets": 42,
            "Avg_Resolution_Time_Days": 3.5,
            "Client_Impact": 0.78,
            "Customer_Experience_Score": 0.78
        },
        "Jane Smith": {
            "sentiment": -0.25,
            "Priority_Impact": 0.45,
            "Issue_Type_Impact": 0.35,
            "Tickets": 27,
            "Avg_Resolution_Time_Days": 5.2,
            "Client_Impact": 0.62,
            "Customer_Experience_Score": 0.62
        }
    }

if __name__ == "__main__":
    # For testing the script directly
    import time

    class MockJiraFile:
        def __init__(self, path):
            self.file = self
            self.path = path

    mock_file = MockJiraFile('Jira OeKB.xls.xlsx')

    # Benchmark the processing time
    start_time = time.time()
    results, client_metrics = process_jira_file(mock_file)
    end_time = time.time()

    print(f"\nProcessing completed in {end_time - start_time:.2f} seconds")

    # Print client metrics table
    print("\nClient Metrics:")
    print(client_metrics.to_string(index=False))
