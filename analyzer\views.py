from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from .models import JiraFile, AnalysisResult
from .forms import JiraFileUploadForm
from .utils import process_jira_file  # This will be implemented later with NLP
from django.http import JsonResponse, HttpResponse, FileResponse
import pandas as pd
from collections import defaultdict
import json
from django.utils import timezone
from datetime import timedelta
import urllib.parse
import os
from django.conf import settings

@login_required
def ai_agent(request):
    return render(request, 'analyzer/ai_agent.html')

@login_required
def dashboard(request):
    # Order files by analysis_date (newest first), fallback to uploaded_at if analysis_date is null
    jira_files = JiraFile.objects.filter(user=request.user).order_by('-analysis_date', '-uploaded_at')

    # Get client metrics summary data
    client_count = 0
    total_tickets = 0
    client_metrics_summary = None
    actionable_insights = None

    # Get all analysis results for the user, ordered by analysis_date (newest first), fallback to creation date
    analysis_results = AnalysisResult.objects.filter(
        jira_file__user=request.user
    ).order_by('-jira_file__analysis_date', '-created_at')

    # Handle comparison if requested
    comparison_data = None
    current_analysis = None
    comparison_analysis = None
    available_analyses = None
    has_comparisons = False

    if analysis_results.count() >= 2:
        has_comparisons = True
        available_analyses = analysis_results

        if request.GET.get('compare_current') and request.GET.get('compare_with'):
            try:
                current_id = int(request.GET.get('compare_current'))
                compare_id = int(request.GET.get('compare_with'))
                current_analysis = get_object_or_404(
                    AnalysisResult,
                    id=current_id,
                    jira_file__user=request.user
                )
                comparison_analysis = get_object_or_404(
                    AnalysisResult,
                    id=compare_id,
                    jira_file__user=request.user
                )
                comparison_data = calculate_comparison_metrics(current_analysis, comparison_analysis)
            except (ValueError, AnalysisResult.DoesNotExist):
                messages.error(request, "Invalid comparison analyses selected.")

    # Get the latest analysis for actionable insights and client metrics
    latest_analysis = analysis_results.first()
    if latest_analysis:
        if hasattr(latest_analysis, 'actionable_insights') and latest_analysis.actionable_insights:
            actionable_insights = latest_analysis.actionable_insights

        # Debug output for client metrics
        if hasattr(latest_analysis, 'client_metrics') and latest_analysis.client_metrics:
            print(f"Latest analysis client metrics: {latest_analysis.client_metrics}")
            print(f"Number of clients in latest analysis: {len(latest_analysis.client_metrics)}")

    # Initialize data structures to store client metrics
    all_clients = set()
    latest_metrics = {}
    global_sentiment_data = {}

    # Process each analysis result to extract client metrics and build global sentiment tracking
    for analysis in analysis_results:
        if not analysis.client_metrics:
            continue

        # Use analysis_date if available, otherwise fall back to created_at
        if analysis.jira_file.analysis_date:
            analysis_date = analysis.jira_file.analysis_date
        else:
            analysis_date = analysis.created_at.date()

        # Process each client's metrics
        for client_name, metrics in analysis.client_metrics.items():
            all_clients.add(client_name)

            # Only update latest_metrics if we haven't seen this client before (to get latest metrics)
            if client_name not in latest_metrics:
                latest_metrics[client_name] = metrics

            # Build global sentiment tracking data
            if client_name not in global_sentiment_data:
                global_sentiment_data[client_name] = []

            global_sentiment_data[client_name].append({
                'date': analysis_date,
                'sentiment': metrics.get('sentiment', 0),
                'tickets': metrics.get('Tickets', 0),
                'resolution_time': metrics.get('Avg_Resolution_Time_Days', 0),
                'analysis_id': analysis.id
            })

    # Calculate sentiment trends and current status for each client
    sentiment_analysis_data = {}
    for client_name in all_clients:
        if client_name in global_sentiment_data and global_sentiment_data[client_name]:
            # Sort by date (newest first)
            client_data = sorted(global_sentiment_data[client_name], key=lambda x: x['date'], reverse=True)

            current_sentiment = client_data[0]['sentiment']
            last_update = client_data[0]['date']

            # Calculate trend if we have multiple data points
            trend = 'stable'
            if len(client_data) > 1:
                # Compare current sentiment with previous sentiment
                previous_sentiment = client_data[1]['sentiment']
                sentiment_change = current_sentiment - previous_sentiment

                if sentiment_change > 0.1:
                    trend = 'improving'
                elif sentiment_change < -0.1:
                    trend = 'declining'
                else:
                    trend = 'stable'

            # Determine severity level
            severity = 'neutral'
            if current_sentiment < -0.5:
                severity = 'critical'
            elif current_sentiment < -0.4:
                severity = 'high'
            elif current_sentiment < -0.2:
                severity = 'medium'
            elif current_sentiment < 0:
                severity = 'low'
            else:
                severity = 'neutral'

            sentiment_analysis_data[client_name] = {
                'current_sentiment': current_sentiment,
                'trend': trend,
                'severity': severity,
                'last_update': last_update.strftime('%Y-%m-%d'),  # Convert date to string
                'tickets': client_data[0]['tickets'],
                'resolution_time': client_data[0]['resolution_time'],
                'data_points': len(client_data)
            }

    # Count total clients
    client_count = len(all_clients)

    # Calculate total tickets by summing issue_count from all processed files
    # This ensures we count all tickets across all files
    processed_analysis_results = analysis_results.filter(jira_file__processed=True)
    total_tickets = sum(analysis.issue_count for analysis in processed_analysis_results)

    # Prepare client metrics summary for charts
    client_metrics_summary = {
        'sentiment': {
            'positive': 0,
            'neutral': 0,
            'negative': 0
        },
        'resolution_time': [],
        'client_impact': []
    }

    # Process client metrics for summary
    for client, metrics in latest_metrics.items():
        # Sentiment counts
        if metrics.get('sentiment', 0) > 0.3:
            client_metrics_summary['sentiment']['positive'] += 1
        elif metrics.get('sentiment', 0) < -0.3:
            client_metrics_summary['sentiment']['negative'] += 1
        else:
            client_metrics_summary['sentiment']['neutral'] += 1

        # Resolution time data
        if 'Avg_Resolution_Time_Days' in metrics:
            client_metrics_summary['resolution_time'].append({
                'client': client,
                'days': metrics['Avg_Resolution_Time_Days']
            })

        # Customer Experience Score data
        if 'Client_Impact' in metrics:
            client_metrics_summary['client_impact'].append({
                'client': client,
                'impact': float(metrics['Client_Impact'])
            })
        # Fallback if Client_Impact is missing but we have Customer_Experience_Score
        elif 'Customer_Experience_Score' in metrics:
            client_metrics_summary['client_impact'].append({
                'client': client,
                'impact': float(metrics['Customer_Experience_Score'])
            })
        # Fallback if both are missing but we have sentiment
        elif 'sentiment' in metrics:
            client_metrics_summary['client_impact'].append({
                'client': client,
                'impact': float(metrics.get('sentiment', 0))
            })

    # Ensure client metrics summary is properly formatted
    if client_metrics_summary and 'client_impact' in client_metrics_summary:
        # Make sure we have at least one client with impact data
        if len(client_metrics_summary['client_impact']) == 0:
            # If no clients have impact data, add a placeholder
            client_metrics_summary['client_impact'].append({
                'client': 'No Data',
                'impact': 0
            })



    context = {
        'jira_files': jira_files,
        'client_count': client_count,
        'total_tickets': total_tickets,
        'client_metrics_summary': client_metrics_summary,
        'latest_analysis': latest_analysis,
        'actionable_insights': actionable_insights,
        'available_analyses': available_analyses,
        'comparison_data': comparison_data,
        'current_analysis': current_analysis,
        'comparison_analysis': comparison_analysis,
        'has_comparisons': has_comparisons,
        'sentiment_analysis_data': sentiment_analysis_data,
    }

    return render(request, 'analyzer/dashboard.html', context)

@login_required
def upload_file(request):
    if request.method == 'POST':
        form = JiraFileUploadForm(request.POST, request.FILES)
        if form.is_valid():
            jira_file = form.save(commit=False)
            jira_file.user = request.user
            # The analysis_date is already handled by the form since it's in the Meta fields
            jira_file.save()

            messages.success(request, f"File uploaded successfully with analysis date {jira_file.analysis_date}. Processing will begin shortly.")
            return redirect('process_file', file_id=jira_file.id)
    else:
        form = JiraFileUploadForm()

    return render(request, 'analyzer/upload.html', {'form': form})

@login_required
def process_file(request, file_id):
    jira_file = get_object_or_404(JiraFile, id=file_id, user=request.user)

    try:
        # Process the file using the NLP utility
        analysis_results, client_metrics_df = process_jira_file(jira_file)

        # Delete any existing analysis results for this file
        jira_file.analysis_results.all().delete()

        # Create a new analysis result with the processed data
        analysis = AnalysisResult.objects.create(
            jira_file=jira_file,
            **analysis_results
        )

        # Mark the file as processed
        jira_file.processed = True
        jira_file.save()

        # Debug output
        print("Analysis results saved to database:")
        print(f"Client metrics: {analysis.client_metrics}")
        print(f"Client metrics type: {type(analysis.client_metrics)}")
        print(f"Number of clients: {len(analysis.client_metrics) if analysis.client_metrics else 0}")

        messages.success(request, "File processed successfully!")
        return redirect('view_analysis', analysis_id=analysis.id)

    except Exception as e:
        import traceback
        print(f"Error processing file: {str(e)}")
        print(traceback.format_exc())
        messages.error(request, f"Error processing file: {str(e)}")
        return redirect('dashboard')

def calculate_comparison_metrics(current_analysis, comparison_analysis):
    """Calculate comparison metrics between two analyses - only for common clients"""
    comparison_data = {
        'client_comparisons': {},
        'overall_changes': {},
        'has_common_clients': False
    }

    current_metrics = current_analysis.client_metrics or {}
    comparison_metrics = comparison_analysis.client_metrics or {}

    # Get only common clients from both analyses (intersection)
    common_clients = set(current_metrics.keys()) & set(comparison_metrics.keys())

    if not common_clients:
        return comparison_data

    comparison_data['has_common_clients'] = True

    for client in common_clients:
        current_client = current_metrics[client]
        comparison_client = comparison_metrics[client]

        # Calculate changes for each metric
        client_comparison = {}

        # Sentiment change
        current_sentiment = current_client.get('sentiment', 0)
        comparison_sentiment = comparison_client.get('sentiment', 0)
        sentiment_change = current_sentiment - comparison_sentiment
        client_comparison['sentiment_change'] = sentiment_change

        # Resolution time change
        current_resolution = current_client.get('Avg_Resolution_Time_Days', 0)
        comparison_resolution = comparison_client.get('Avg_Resolution_Time_Days', 0)
        resolution_change = current_resolution - comparison_resolution
        client_comparison['resolution_time_change'] = resolution_change

        # Customer experience score change
        current_impact = current_client.get('Client_Impact', 0)
        comparison_impact = comparison_client.get('Client_Impact', 0)
        impact_change = current_impact - comparison_impact
        client_comparison['client_impact_change'] = impact_change

        # Ticket count change
        current_tickets = current_client.get('Tickets', 0)
        comparison_tickets = comparison_client.get('Tickets', 0)
        tickets_change = current_tickets - comparison_tickets
        client_comparison['tickets_change'] = tickets_change

        # Store current and comparison values for display
        client_comparison['current'] = current_client
        client_comparison['comparison'] = comparison_client

        comparison_data['client_comparisons'][client] = client_comparison

    # Calculate overall changes (only for common clients)
    if common_clients:
        # Average sentiment change
        sentiment_changes = [comp['sentiment_change'] for comp in comparison_data['client_comparisons'].values()]
        comparison_data['overall_changes']['avg_sentiment_change'] = sum(sentiment_changes) / len(sentiment_changes) if sentiment_changes else 0

        # Average resolution time change
        resolution_changes = [comp['resolution_time_change'] for comp in comparison_data['client_comparisons'].values()]
        comparison_data['overall_changes']['avg_resolution_change'] = sum(resolution_changes) / len(resolution_changes) if resolution_changes else 0

        # Average client impact change
        impact_changes = [comp['client_impact_change'] for comp in comparison_data['client_comparisons'].values()]
        comparison_data['overall_changes']['avg_impact_change'] = sum(impact_changes) / len(impact_changes) if impact_changes else 0

    return comparison_data

@login_required
def view_analysis(request, analysis_id):
    analysis = get_object_or_404(AnalysisResult, id=analysis_id)
    jira_file = analysis.jira_file

    # Ensure the user has permission to view this analysis
    if jira_file.user != request.user:
        messages.error(request, "You don't have permission to view this analysis.")
        return redirect('dashboard')



    # Debug: Print client metrics to console
    print("Client Metrics in view_analysis:", analysis.client_metrics)
    print("Client Metrics type:", type(analysis.client_metrics))

    context = {
        'analysis': analysis,
        'jira_file': jira_file,
    }
    return render(request, 'analyzer/view_analysis.html', context)

def home(request):
    if request.user.is_authenticated:
        return redirect('overview')
    return render(request, 'analyzer/home.html')

@login_required
def overview(request):
    """
    Display the overview page with information about NLP analysis and client metrics.
    This serves as the landing page after authentication.
    """
    return render(request, 'analyzer/overview.html')

@login_required
def regenerate_analysis(request, file_id):
    """Regenerate analysis for an existing file"""
    jira_file = get_object_or_404(JiraFile, id=file_id, user=request.user)

    try:
        # Process the file using the NLP utility
        analysis_results, client_metrics_df = process_jira_file(jira_file)

        # Delete existing analysis results
        jira_file.analysis_results.all().delete()

        # Create new analysis result
        analysis = AnalysisResult.objects.create(
            jira_file=jira_file,
            **analysis_results
        )

        # Debug output
        print("Analysis regenerated with client metrics:")
        print(f"Client metrics: {analysis.client_metrics}")
        print(f"Client metrics type: {type(analysis.client_metrics)}")

        messages.success(request, "Analysis regenerated successfully!")
        return redirect('view_analysis', analysis_id=analysis.id)

    except Exception as e:
        messages.error(request, f"Error regenerating analysis: {str(e)}")
        return redirect('dashboard')

@login_required
def debug_analysis(request, analysis_id):
    """Debug view to directly output analysis data as JSON"""
    analysis = get_object_or_404(AnalysisResult, id=analysis_id)

    # Ensure the user has permission to view this analysis
    if analysis.jira_file.user != request.user:
        return JsonResponse({"error": "Permission denied"}, status=403)

    # Return all analysis data as JSON
    return JsonResponse({
        "analysis_id": analysis.id,
        "file_name": analysis.jira_file.file.name,
        "issue_count": analysis.issue_count,
        "ticket_types": analysis.ticket_types,
        "priority_distribution": analysis.priority_distribution,
        "status_distribution": analysis.status_distribution,
        "common_themes": analysis.common_themes,
        "sentiment_analysis": analysis.sentiment_analysis,
        "client_metrics": analysis.client_metrics,
        "client_metrics_type": str(type(analysis.client_metrics))
    })

@login_required
def download_cleaned_data(request, analysis_id):
    """Download the cleaned JIRA data with impact scores as CSV"""
    analysis = get_object_or_404(AnalysisResult, id=analysis_id)

    # Ensure the user has permission to download this data
    if analysis.jira_file.user != request.user:
        messages.error(request, "You don't have permission to download this data.")
        return redirect('dashboard')

    # Path to the CSV file
    file_path = os.path.join(settings.BASE_DIR, 'cleaned_jira_data_with_impact.csv')

    # Check if file exists
    if not os.path.exists(file_path):
        messages.error(request, "The cleaned data file could not be found.")
        return redirect('view_analysis', analysis_id=analysis_id)

    # Serve the file for download
    response = FileResponse(open(file_path, 'rb'), as_attachment=True, filename='cleaned_jira_data_with_impact.csv')
    return response

@login_required
def delete_file(request, file_id):
    """Delete a JIRA file and its associated analysis results"""
    jira_file = get_object_or_404(JiraFile, id=file_id, user=request.user)

    # Get the file name for the success message
    file_name = jira_file.file.name

    try:
        # Delete the file and all associated analysis results
        jira_file.delete()
        messages.success(request, f"File '{file_name}' has been deleted successfully.")
    except Exception as e:
        messages.error(request, f"Error deleting file: {str(e)}")

    return redirect('dashboard')



@login_required
def client_overview(request):
    """View for the client overview page showing all clients and their metrics over time"""

    # Get all analysis results for the user, ordered by analysis_date (newest first), fallback to creation date
    analysis_results = AnalysisResult.objects.filter(
        jira_file__user=request.user,
        jira_file__processed=True  # Only include processed files
    ).order_by('-jira_file__analysis_date', '-created_at')

    # Initialize data structures to store client metrics over time
    clients_data = defaultdict(list)
    all_clients = set()
    timeline_dates = []

    # Process each analysis result to extract client metrics over time
    for analysis in analysis_results:
        if not analysis.client_metrics:
            continue

        # Use analysis_date if available, otherwise fall back to created_at
        if analysis.jira_file.analysis_date:
            analysis_date = analysis.jira_file.analysis_date.strftime('%Y-%m-%d')
        else:
            analysis_date = analysis.created_at.strftime('%Y-%m-%d')

        if analysis_date not in timeline_dates:
            timeline_dates.append(analysis_date)

        # Process each client's metrics
        for client_name, metrics in analysis.client_metrics.items():
            all_clients.add(client_name)

            # Store the metrics with the date, handling both Client_Impact and Customer_Experience_Score
            client_impact = metrics.get('Client_Impact', 0)
            if client_impact == 0:
                client_impact = metrics.get('Customer_Experience_Score', 0)

            clients_data[client_name].append({
                'date': analysis_date,
                'sentiment': metrics.get('sentiment', 0),
                'priority_impact': metrics.get('Priority_Impact', 0),
                'issue_type_impact': metrics.get('Issue_Type_Impact', 0),
                'tickets': metrics.get('Tickets', 0),
                'resolution_time': metrics.get('Avg_Resolution_Time_Days', 0),
                'client_impact': client_impact,
                'analysis_id': analysis.id
            })

    # Sort timeline dates chronologically
    timeline_dates.sort()

    # Get the latest metrics for each client for the summary view
    latest_metrics = {}
    for client in all_clients:
        if clients_data[client]:
            # Sort by date (newest first) and take the first item
            sorted_data = sorted(clients_data[client], key=lambda x: x['date'], reverse=True)
            latest_metrics[client] = sorted_data[0]

    # Calculate trend data for each client
    client_trends = {}
    for client in all_clients:
        if len(clients_data[client]) > 1:
            # Sort by date (oldest first)
            sorted_data = sorted(clients_data[client], key=lambda x: x['date'])

            # Calculate trends
            first_metrics = sorted_data[0]
            last_metrics = sorted_data[-1]

            client_trends[client] = {
                'sentiment_change': last_metrics['sentiment'] - first_metrics['sentiment'],
                'resolution_time_change': last_metrics['resolution_time'] - first_metrics['resolution_time'],
                'client_impact_change': last_metrics['client_impact'] - first_metrics['client_impact'],
                'tickets_change': last_metrics['tickets'] - first_metrics['tickets'],
                'data_points': len(sorted_data)
            }

    # Sort clients by client impact in ascending order
    sorted_clients = []
    for client in all_clients:
        if client in latest_metrics:
            # Get client_impact from the processed metrics
            client_impact = latest_metrics[client].get('client_impact', 0)
            sorted_clients.append((client, client_impact))

    # Sort by client impact (descending order)
    sorted_clients.sort(key=lambda x: x[1], reverse=True)

    # Extract just the client names in the sorted order
    sorted_client_names = [client[0] for client in sorted_clients]

    # Calculate summary metrics
    total_resolution_time = 0
    total_client_impact = 0
    client_count = 0

    # Calculate total tickets by summing issue_count from all processed files
    # This ensures we count all tickets across all files
    processed_analysis_results = analysis_results.filter(jira_file__processed=True)
    total_tickets = sum(analysis.issue_count for analysis in processed_analysis_results)

    for client in all_clients:
        if client in latest_metrics:
            metrics = latest_metrics[client]
            total_resolution_time += metrics.get('resolution_time', 0)
            total_client_impact += metrics.get('client_impact', 0)
            client_count += 1

    # Calculate averages
    avg_resolution_time = total_resolution_time / client_count if client_count > 0 else 0
    avg_client_impact = total_client_impact / client_count if client_count > 0 else 0

    context = {
        'all_clients': sorted_client_names,
        'latest_metrics': latest_metrics,
        'client_trends': client_trends,
        'timeline_dates': timeline_dates,
        'clients_data_json': json.dumps(clients_data),
        'has_data': len(all_clients) > 0,
        'total_tickets': total_tickets,
        'avg_resolution_time': avg_resolution_time,
        'avg_client_impact': avg_client_impact
    }

    return render(request, 'analyzer/client_overview.html', context)

@login_required
def team_overview(request):
    """View for the team overview page showing team performance metrics"""

    # This is a placeholder view that will be expanded later
    # Currently just renders the template with no dynamic data

    return render(request, 'analyzer/team_overview.html')

@login_required
def client_detail(request, client_name):
    """View for detailed metrics of a specific client over time"""

    # URL decode the client name
    client_name = urllib.parse.unquote(client_name)

    # Get all analysis results for the user, ordered by analysis_date (newest first), fallback to creation date
    analysis_results = AnalysisResult.objects.filter(
        jira_file__user=request.user,
        jira_file__processed=True  # Only include processed files
    ).order_by('-jira_file__analysis_date', '-created_at')

    # Initialize data structures
    client_metrics_over_time = []
    timeline_dates = []
    client_ticket_types = {}
    latest_analysis = None
    total_client_tickets = 0

    # Process each analysis result to extract this client's metrics over time
    for analysis in analysis_results:
        # Skip if analysis has no client metrics
        if not analysis.client_metrics:
            continue

        # If this client isn't in the current analysis but we have other analyses,
        # we'll still process the file to get ticket type distributions
        has_client_data = client_name in analysis.client_metrics

        # Store the latest analysis that has data (even if not for this specific client)
        if latest_analysis is None:
            latest_analysis = analysis

        # If this analysis has data for our client, process it
        if has_client_data:
            metrics = analysis.client_metrics[client_name]

            # Use analysis_date if available, otherwise fall back to created_at
            if analysis.jira_file.analysis_date:
                analysis_date = analysis.jira_file.analysis_date.strftime('%Y-%m-%d')
            else:
                analysis_date = analysis.created_at.strftime('%Y-%m-%d')

            # Add the date to our timeline
            if analysis_date not in timeline_dates:
                timeline_dates.append(analysis_date)

            # Get ticket count for this client in this analysis
            client_tickets_in_analysis = int(metrics.get('Tickets', 0))
            total_client_tickets += client_tickets_in_analysis

            # Store the metrics with the date
            client_metrics_over_time.append({
                'date': analysis_date,
                'sentiment': metrics.get('sentiment', 0),
                'priority_impact': metrics.get('Priority_Impact', 0),
                'issue_type_impact': metrics.get('Issue_Type_Impact', 0),
                'tickets': client_tickets_in_analysis,
                'resolution_time': metrics.get('Avg_Resolution_Time_Days', 0),
                'client_impact': metrics.get('Client_Impact', 0) if 'Client_Impact' in metrics else metrics.get('Customer_Experience_Score', 0),
                'analysis_id': analysis.id,
                'file_name': analysis.jira_file.file.name
            })

            # No need to track client_ticket_count separately anymore

    # Sort timeline dates chronologically
    timeline_dates.sort()

    # Sort metrics by date (oldest first for trend analysis)
    client_metrics_over_time = sorted(client_metrics_over_time, key=lambda x: x['date'])

    # Calculate trends if we have enough data points
    trends = None
    if len(client_metrics_over_time) > 1:
        first_metrics = client_metrics_over_time[0]
        last_metrics = client_metrics_over_time[-1]

        trends = {
            'sentiment_change': last_metrics['sentiment'] - first_metrics['sentiment'],
            'resolution_time_change': last_metrics['resolution_time'] - first_metrics['resolution_time'],
            'client_impact_change': last_metrics['client_impact'] - first_metrics['client_impact'],
            'data_points': len(client_metrics_over_time)
        }

    # Aggregate ticket types across all analyses for this client
    excluded_prefixes = ['generated at', 'created at', 'updated at', 'date']
    excluded_keywords = ['timestamp', 'time', 'date', 'utc', 'gmt']

    # Initialize combined ticket types dictionary
    client_ticket_types = {}

    # Make sure we have the total tickets for this client
    if not total_client_tickets:
        total_client_tickets = sum(metric['tickets'] for metric in client_metrics_over_time)

    # Process each analysis result to extract and combine ticket types
    for analysis in analysis_results:
        if not analysis.client_metrics or client_name not in analysis.client_metrics:
            continue

        # Get client's ticket count in this analysis
        client_tickets_in_analysis = int(analysis.client_metrics[client_name].get('Tickets', 0))

        if client_tickets_in_analysis > 0 and analysis.ticket_types:
            # Calculate what portion of the total tickets this analysis represents
            analysis_weight = client_tickets_in_analysis / total_client_tickets if total_client_tickets > 0 else 0

            # Process each ticket type in this analysis
            for ticket_type, count in analysis.ticket_types.items():
                # Skip entries that look like timestamps or dates
                if any(ticket_type.lower().startswith(prefix) for prefix in excluded_prefixes):
                    continue
                if any(keyword in ticket_type.lower() for keyword in excluded_keywords):
                    continue

                # Calculate this client's portion of this ticket type
                # Scale by the client's proportion of tickets in this analysis
                client_portion = round(int(count) * analysis_weight)

                # Add to the combined ticket types
                if client_portion > 0:
                    if ticket_type in client_ticket_types:
                        client_ticket_types[ticket_type] += client_portion
                    else:
                        client_ticket_types[ticket_type] = client_portion

    # If no ticket types were found, use a default
    if not client_ticket_types and total_client_tickets > 0:
        client_ticket_types = {"No categorized tickets": total_client_tickets}

    # Ensure the total of ticket types matches the total client tickets
    current_total = sum(client_ticket_types.values())
    if current_total != total_client_tickets and client_ticket_types and total_client_tickets > 0:
        # Find the largest category to adjust
        largest_category = max(client_ticket_types.items(), key=lambda x: x[1])[0]
        # Adjust it to make the total match
        adjustment = total_client_tickets - current_total
        client_ticket_types[largest_category] += adjustment

    context = {
        'client_name': client_name,
        'metrics_over_time': client_metrics_over_time,
        'timeline_dates': timeline_dates,
        'trends': trends,
        'metrics_json': json.dumps(client_metrics_over_time),
        'ticket_types_json': json.dumps(client_ticket_types),
        'has_data': len(client_metrics_over_time) > 0
    }

    return render(request, 'analyzer/client_detail.html', context)
