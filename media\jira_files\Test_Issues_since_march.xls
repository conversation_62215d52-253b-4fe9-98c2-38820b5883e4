<html xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:x="urn:schemas-microsoft-com:office:excel"
xmlns="http://www.w3.org/TR/REC-html40">
<head>
<title><PERSON>ra</title>
<style type="text/css">
table {
    mso-displayed-decimal-separator:"\.";
    mso-displayed-thousand-separator:"\,";
}
body
{
    margin: 0px;
    font-size: 12px;
    font-family: Arial, sans-serif;
    color:black;
}

</style>
<META HTTP-EQUIV="Content-Type" Content="application/vnd.ms-excel; charset=UTF-8">
<!-- JRA-7598 - ensure fields (e.g. description) occupy only one cell - even fields containing newlines. -->
<!--
Vertical align all cells to the top, in order to align all issue rows of issuetable to the top,
since Excel does not use or save the css files it is hardcoded here.
-->
<style>
@page
{
mso-page-orientation:landscape;
margin:.25in .25in .5in .25in;
mso-header-margin:.5in;
mso-footer-margin:.25in;
mso-footer-data:"&R&P of &N";
mso-horizontal-page-align:center;
mso-vertical-page-align:center;
}

td.issuekey,
td.issuetype,
td.status {
    mso-style-parent: "";
    mso-number-format: \@;
    text-align: left;
}
br
{
    mso-data-placement:same-cell;
}

td
{
    vertical-align: top;
}
</style>

<!--[if gte mso 9]><xml>
<x:ExcelWorkbook>
<x:ExcelWorksheets>
<x:ExcelWorksheet>
<x:Name>general_report</x:Name>
<x:WorksheetOptions>
<x:Print>
<x:ValidPrinterInfo/>
</x:Print>
</x:WorksheetOptions>
</x:ExcelWorksheet>
</x:ExcelWorksheets>
</x:ExcelWorkbook>
</xml><![endif]-->
</head>
<body>

<table border="1">
    <tr bgcolor="#0747a6" height="27">
        <td colspan="15">
            <img src="https://jira.vermeg.com/jira-logo-scaled.png" width="75" height="27" border="0" alt="Jira">
        </td>
    </tr>
    <tr>
        <td colspan="15">
            <a href="https://jira.vermeg.com/issues/?jql=project+%3D+OEKB+AND+issuetype+%3D+Incident+AND+cf%5B12629%5D+in+%28%22M12+Platform%22%2C+%22SCORE+Project%22%2C+%22Simulation+Platform%22%2C+%22Test+Platform%22%29+AND+created+%3E%3D+2025-03-01">Jira</a>
        </td>
    </tr>
    <tr>
        <td colspan="15">
            Displaying <strong>66</strong> issues at <strong>20/May/25 11:46 AM</strong>.
        </td>
    </tr>
</table>




                            <issuetable-web-component data-content="issues">
                <table id="issuetable"  border="1" cellpadding="3" cellspacing="1" width="100%">
                        <thead>
        <tr class="rowHeader">
            
                                                            <th class="colHeaderLink headerrow-issuetype" data-id="issuetype">
                            Issue Type
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-issuekey" data-id="issuekey">
                            Key
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-issuelinks" data-id="issuelinks">
                            Linked Issues
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-priority" data-id="priority">
                            Priority
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-summary" data-id="summary">
                            Summary
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-status" data-id="status">
                            Status
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-assignee" data-id="assignee">
                            Assignee
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-creator" data-id="creator">
                            Creator
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-created" data-id="created">
                            Created
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-fixVersions" data-id="fixVersions">
                            Fix Version/s
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-customfield_12663" data-id="customfield_12663">
                            Date of First Response
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-resolutiondate" data-id="resolutiondate">
                            Resolved
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-customfield_12503" data-id="customfield_12503">
                            Redeclared
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-customfield_20812" data-id="customfield_20812">
                            Linked issue
                                                    </th>
                                                
                                                            <th class="colHeaderLink headerrow-description" data-id="description">
                            Description
                                                    </th>
                                                                    </tr>
    </thead>
    <tbody>
                    

                <tr id="issuerow1571182" rel="1571182" data-issuekey="OEKB-6887" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6887" href="https://jira.vermeg.com/browse/OEKB-6887">OEKB-6887</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 SCoRE Std. 15 - 15022 OUT could not be generated due to long COAF
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 16/May/25 10:15 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663"></td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Yesterday I created some events with seev.031, as they were come from Clearstream (attached). As in 20022 standard it is allowed to have 35 characters in reference fields I didn&#39;t take care that for COAF I took more than 16 characters which is the limit on 15022 standard.
<br/>

<br/>
Now the generation of all 15022 OUT failed in MegaBroker due to following error:
<br/>

<br/>
I understand the reason for this error, but as this could happen everytime if the custodian sends 20022 and our clients receive 15022: What is your solution for this topic? Has this been discussed in rounds concerning the new ISO20022 standards what happens if references (or maybe also other fields) have more characters space in 20022 than in 15022? What should happen then?
<br/>

<br/>
At the moment, the clients wouldn&#39;t receive any notification for events which were created by ISO20022 with a COAF greater than 16 characters.
<br/>

<br/>
Please advise how this should work and what your thoughts are about this situation.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1570491" rel="1570491" data-issuekey="OEKB-6880" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6880" href="https://jira.vermeg.com/browse/OEKB-6880">OEKB-6880</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 SCoRE Std. 01 - DRAW-event wrong calculation
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 14/May/25 2:30 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663"></td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Event DRAW0000005242
<br/>

<br/>
I took an example-SWIFT from production and just changed some figures and dates and created a DRAW-event in QAS - this worked so far.
<br/>

<br/>
Unfortunately when calculating entitlements and sending REPE-SWIFTs I noticed that MegaCor would debit 100% of the shares which is not correct.
<br/>

<br/>
In our opinion the system should calculate the redemption rate and calculate it for every sec account separately instead of taking 100% for every client.
<br/>

<br/>
Further: In View-Screen there is a redemption rate in SEC OUT part visible, in Create- and Update-Screen this field is not visible (see screenshots).
<br/>

<br/>
Attached you find the MT564-in and two examples of MT564 REPE and seev.035 REPE OUT.
<br/>

<br/>
Please check and explain how the system should work and what is the correct behaviour in these cases and why the redemption rate is only visible in VIEW-Screen.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1569548" rel="1569548" data-issuekey="OEKB-6876" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6876" href="https://jira.vermeg.com/browse/OEKB-6876">OEKB-6876</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Cosmetic
    </td>
                                            <td class="summary"><p>
                M12 QAS: Exercice instead of Exercise
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 12/May/25 1:24 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663"></td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      We had this topic already - please change the wording of Exercice to Exercise as it is seen by clients in ASOC.
<br/>

<br/>
I found it in the EXWA MAND event now. In EXWA CHOS it is called “Exercise” and in EXWA VOLU it is called “EXER”.
<br/>

<br/>
Please make sure that it is correct on all places.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1568306" rel="1568306" data-issuekey="OEKB-6868" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6868" href="https://jira.vermeg.com/browse/OEKB-6868">OEKB-6868</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Critical
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 8 NTS - REPE to 3i not generated
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 07/May/25 10:09 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="07/May/25 2:01 PM"><time class="livestamp allow-future" datetime="2025-05-07T14:01:38+0100">07/May/25 2:01 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 07/May/25 2:01 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      For three events, the entitlements have been calculated correctly after full load and REPE has been generated and sent: INTR0000004353, INTR0000004358, DVCA0000005239
<br/>

<br/>
Unfortunately we cannot open the REPE to 3i.
<br/>

<br/>
In MegaBroker, the three FlowINs came in with ERROR=TRUE with the attached error message and no Flow Out is found.
<br/>

<br/>
Please check why these REPEs now do not come to 3i.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1568281" rel="1568281" data-issuekey="OEKB-6867" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6867" href="https://jira.vermeg.com/browse/OEKB-6867">OEKB-6867</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCore: Std. 15 - BPUT: missing tag in outgoing seev.031
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 07/May/25 9:27 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663"></td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Event BPUT0000003496
<br/>

<br/>
This event has the following election period (without begin date) in both options in MegaCor.
<br/>

<br/>
A NEWM and a RMDR have been sent out.
<br/>

<br/>
The result in MT564: „:69E::PWAL//UKWN/20250502“ which looks good.
<br/>

<br/>
The result in seev.031 is unfortunately the followint for both options in both messages the same (see attachment).
<br/>

<br/>
Due to that, the following error message is received in SWIFT-testtool (see attachment).
<br/>

<br/>
Please check and correct that.
<br/>

<br/>
Thanks,
<br/>
stefan
<br/>

<br/>
 
<br/>

<br/>
 
  </td>
                    </tr>


                <tr id="issuerow1568030" rel="1568030" data-issuekey="OEKB-6864" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6864" href="https://jira.vermeg.com/browse/OEKB-6864">OEKB-6864</a>
</td>
                                            <td class="issuelinks">                                        PMG-42727                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 4 - Rounding of fees
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 06/May/25 1:34 PM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="07/May/25 10:44 AM"><time class="livestamp allow-future" datetime="2025-05-07T10:44:18+0100">07/May/25 10:44 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Event SHPR0000004884
<br/>

<br/>
Fees per amount in MegaCor: 0,033333
<br/>

<br/>
Example Client Sec Account 222100: 1.481 shares
<br/>

<br/>
MegaCor calculated fees of 49,36 but when we checked it turned out that it was round down by error: 1.481*0,033333=49,366173 which should lead to 49,37 fees
<br/>

<br/>
Attached you find outgoing SWIFTs which show this amound and also the entitlement and the payment of this client.
<br/>

<br/>
Please check that.
<br/>

<br/>
Thanks, stefan
  </td>
                    </tr>


                <tr id="issuerow1567468" rel="1567468" data-issuekey="OEKB-6861" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6861" href="https://jira.vermeg.com/browse/OEKB-6861">OEKB-6861</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 QAS - TEND0000005231 - seev.033 IN - FlowOut in status &quot;WaitingAck&quot;
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 05/May/25 8:47 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="05/May/25 10:40 AM"><time class="livestamp allow-future" datetime="2025-05-05T10:40:22+0100">05/May/25 10:40 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 05/May/25 11:09 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      TEND0000005231
<br/>
When trying to import a client instruction (seev.033 attached), in MegaBroker, the FlowIn is in status “ValidFlow”, but the FlowOut is still in status “WaitingAck” and did not reach MegaCor.
<br/>

<br/>
Could you please check what is wrong here?
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1566621" rel="1566621" data-issuekey="OEKB-6853" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6853" href="https://jira.vermeg.com/browse/OEKB-6853">OEKB-6853</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 15 - EXWA CASH Event - seev.035 Cash price is missing
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 30/Apr/25 9:53 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="30/Apr/25 9:58 AM"><time class="livestamp allow-future" datetime="2025-04-30T09:58:04+0100">30/Apr/25 9:58 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      test with EXWA0000005046
<br/>

<br/>
Hello,
<br/>

<br/>
the seev.035 message does not include the cash price received per product.
<br/>

<br/>
The MT564REPE provides this information.
<br/>

<br/>
 
<br/>

<br/>
Please check\!
<br/>

<br/>
 
<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1566618" rel="1566618" data-issuekey="OEKB-6852" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6852" href="https://jira.vermeg.com/browse/OEKB-6852">OEKB-6852</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 15 - PRED Event seev.036 Incorrect redemption rate and missing pool factor
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 30/Apr/25 9:51 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="30/Apr/25 9:59 AM"><time class="livestamp allow-future" datetime="2025-04-30T09:59:36+0100">30/Apr/25 9:59 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      test with PRED0000005071
<br/>

<br/>
Hello,
<br/>

<br/>
for the PRED event mentioned above, the redemption rate on the seev.036 message is incorrect. It is not identical to the redemption rate on the MT566NEWM message.
<br/>

<br/>
However, the amount is calculated correctly.
<br/>

<br/>
The pool factor (_NWFC_ and PRFC) are also missing.
<br/>

<br/>
Please check\!
<br/>

<br/>
 
<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1566612" rel="1566612" data-issuekey="OEKB-6851" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6851" href="https://jira.vermeg.com/browse/OEKB-6851">OEKB-6851</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 15 - PRED Event seev.035 Incorrect redemption rate and missing pool factor
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 30/Apr/25 9:45 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="30/Apr/25 9:59 AM"><time class="livestamp allow-future" datetime="2025-04-30T09:59:25+0100">30/Apr/25 9:59 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      test with PRED0000005071
<br/>

<br/>
Hello,
<br/>

<br/>
for the PRED event mentioned above, the redemption rate on the seev.035 message is incorrect. It is not identical to the redemption rate on the MT564REPE message.
<br/>

<br/>
However, the entitlements are calculated correctly.
<br/>

<br/>
The pool factor (_NWFC_ and PRFC) are also missing.
<br/>

<br/>
Please check\!
<br/>

<br/>
 
<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1566610" rel="1566610" data-issuekey="OEKB-6850" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6850" href="https://jira.vermeg.com/browse/OEKB-6850">OEKB-6850</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - Standard 15: PRED Event seev.031 Incorrect redemption rate
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 30/Apr/25 9:37 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="30/Apr/25 9:48 AM"><time class="livestamp allow-future" datetime="2025-04-30T09:48:06+0100">30/Apr/25 9:48 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      test with PRED0000005071
<br/>

<br/>
Hello,
<br/>

<br/>
for the PRED event mentioned above, the redemption rate on the seev.031 and seev.031 REPL message is incorrect. It is not identical to the one on the MT564NEWM and MT564REPL message.
<br/>

<br/>
Please check\!
<br/>

<br/>
 
<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1563875" rel="1563875" data-issuekey="OEKB-6831" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6831" href="https://jira.vermeg.com/browse/OEKB-6831">OEKB-6831</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 QAS: Update REDM Price
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 22/Apr/25 3:06 PM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663"></td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Test with REDM0000000336
<br/>

<br/>
 
<br/>

<br/>
Hello, 
<br/>

<br/>
I would like to change the redemption price for the Event I have mentioned above.
<br/>

<br/>
When I try this, there are *three fields* where you can enter the redemption rate, which is not the case in M12 production.
<br/>

<br/>
I have tried to change only the &#39;Redemption Price&#39; but the update is not being saved.
<br/>

<br/>
 
<br/>

<br/>
Please check\!
<br/>

<br/>
 
<br/>

<br/>
Thank you and BR, Dali
  </td>
                    </tr>


                <tr id="issuerow1562258" rel="1562258" data-issuekey="OEKB-6828" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6828" href="https://jira.vermeg.com/browse/OEKB-6828">OEKB-6828</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 QAS: PROX - seev.004 with &quot;Vote For All Agenda Resolutions&quot; rejected
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 17/Apr/25 1:40 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="17/Apr/25 2:33 PM"><time class="livestamp allow-future" datetime="2025-04-17T14:33:07+0100">17/Apr/25 2:33 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 02/May/25 10:11 AM </td>
                                            <td class="customfield_12503">    YES
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Event PROX0000005001
<br/>

<br/>
Due to a case in production we tested this first in QAS and unfortunately a seev.004 instruction from the client with voting for all agenda resolution was immediately rejected by MegaCor due to the attached error.
<br/>

<br/>
You find the seev.004 also attached.
<br/>

<br/>
Please check and fix this as this makes it much easier if an event has many resolution points like this event (23\!).
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1562150" rel="1562150" data-issuekey="OEKB-6825" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6825" href="https://jira.vermeg.com/browse/OEKB-6825">OEKB-6825</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 QAS: PROX - outgoing seev.004 without content
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 17/Apr/25 10:23 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663"></td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Today the generation failed for a seev.04 OUT for event PROX0000005001.
<br/>

<br/>
According to MegaBroker Generation failed due to following error:
<br/>

<br/>
*PALM-15037: The content Austria of the value CtryOfIncorprtn\[Document.MtgInstr.Instr.AcctDtls.RghtsHldr.RghtsHldr_Choice_0.LglPrsn.CtryOfIncorprtn.CtryOfIncorprtn] is not valid. It should verify this pattern \[A-Z]\{2,2}. The length of this value should be between 0 and 2147483647.*
<br/>

<br/>
Anyway I created the PV CI instruction with attached content.
<br/>

<br/>
If the system requires a 2-digit-country-code in both fields it should be mandatory when creating. Better would be to offer a list of possible values. But afterwards an error in message generation when it cannot be edited anymore is no good solution.
<br/>

<br/>
As this will not be done very often this could lead to problems when it is not clear that a code is necessary in both fields.
<br/>

<br/>
Please check this topic - thanks\!
<br/>

<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1561875" rel="1561875" data-issuekey="OEKB-6823" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6823" href="https://jira.vermeg.com/browse/OEKB-6823">OEKB-6823</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Critical
    </td>
                                            <td class="summary"><p>
                M12 QAS: No payment confirmation can be sent
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 16/Apr/25 3:16 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="16/Apr/25 3:39 PM"><time class="livestamp allow-future" datetime="2025-04-16T15:39:34+0100">16/Apr/25 3:39 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 16/Apr/25 3:39 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      We have some examples today where we cannot send payment confirmations although the client payments are in status Created: DVCA0000004991, LIQU0000004992, MCAL0000004993, DVCA0000004994.
<br/>

<br/>
Please check that as this worked already fine hundreds of times.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1561847" rel="1561847" data-issuekey="OEKB-6822" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6822" href="https://jira.vermeg.com/browse/OEKB-6822">OEKB-6822</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 QAS: Tax amount where no tax should be
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 16/Apr/25 2:53 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="17/Apr/25 9:24 AM"><time class="livestamp allow-future" datetime="2025-04-17T09:24:49+0100">17/Apr/25 9:24 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Event SHPR0000004884
<br/>

<br/>
This share premium event has a gross and a fee amount (and of course a net amount) but definitive no tax amount.
<br/>

<br/>
In the client entitlements (and so also in outgoing seev.035/MT564 REPE) there was no tax but unfortunately in the client payments and so also in outgoing seev.036/MT566 the system shows a tax amount in the same amount as the fees which is wrong.
<br/>

<br/>
Please check and correct that for all event types in MegaCor.
<br/>

<br/>
Thanks,
<br/>

<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1561827" rel="1561827" data-issuekey="OEKB-6821" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6821" href="https://jira.vermeg.com/browse/OEKB-6821">OEKB-6821</a>
</td>
                                            <td class="issuelinks">                                        PMG-42664                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 QAS: No fees on MT564 NEWM and MT564 REPE
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 16/Apr/25 1:53 PM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="17/Apr/25 9:25 AM"><time class="livestamp allow-future" datetime="2025-04-17T09:25:43+0100">17/Apr/25 9:25 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Event SHPR0000004884
<br/>

<br/>
Also for MT564 NEWM and MT564 REPE, the fees per share are not included.
<br/>

<br/>
Please check that.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1561823" rel="1561823" data-issuekey="OEKB-6820" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6820" href="https://jira.vermeg.com/browse/OEKB-6820">OEKB-6820</a>
</td>
                                            <td class="issuelinks">                                        PMG-42664                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 15 - Fees missing in seev.031
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 16/Apr/25 1:44 PM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="24/Apr/25 6:04 PM"><time class="livestamp allow-future" datetime="2025-04-24T18:04:42+0100">24/Apr/25 6:04 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Event SHPR0000004884
<br/>

<br/>
although in this event fees are filled in (see attachment)
<br/>

<br/>
they are not included in the outgoing seev.031 (you find it also attached) and also not in the two seev.035 (two seev.035 because of breakdown instruction - you find both attached).
<br/>

<br/>
Further - only in one of these two seev035 there is a total charge amount to find, in the other one, this line is missing.
<br/>

<br/>
Please check why the fees - at least partly - are missing in the SWIFTs
<br/>

<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1561283" rel="1561283" data-issuekey="OEKB-6815" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6815" href="https://jira.vermeg.com/browse/OEKB-6815">OEKB-6815</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 13 - seev.035 without content
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 15/Apr/25 1:41 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="15/Apr/25 3:22 PM"><time class="livestamp allow-future" datetime="2025-04-15T15:22:34+0100">15/Apr/25 3:22 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 15/Apr/25 3:48 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Event EXWA0000004975
<br/>

<br/>
After creating a reversal request the notification seev.035 could be generated, but Generation Failed in MegaBroker due to following error.
<br/>

<br/>
Please check what the problem is with this notification.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1561089" rel="1561089" data-issuekey="OEKB-6813" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6813" href="https://jira.vermeg.com/browse/OEKB-6813">OEKB-6813</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 15 - INTR0000003895 - seev.036 OUT
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Nadia BOUSSETTA
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 15/Apr/25 8:02 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="15/Apr/25 4:06 PM"><time class="livestamp allow-future" datetime="2025-04-15T16:06:07+0100">15/Apr/25 4:06 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 14/May/25 10:59 AM </td>
                                            <td class="customfield_12503">    YES
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      INTR0000003895 
<br/>

<br/>
Hello,
<br/>

<br/>
we have noticed that the following information are not included in the seev.036 messages:
<br/>

<br/>
interest calculation method
<br/>

<br/>
maturity date
<br/>

<br/>
coupon date
<br/>

<br/>
interest period
<br/>

<br/>
Number of Days Accrued
<br/>

<br/>
interest rate
<br/>

<br/>
value date
<br/>

<br/>
 
<br/>

<br/>
Please check\!
<br/>

<br/>
 
<br/>

<br/>
Thank you and BR, Dalibor
  </td>
                    </tr>


                <tr id="issuerow1560935" rel="1560935" data-issuekey="OEKB-6811" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6811" href="https://jira.vermeg.com/browse/OEKB-6811">OEKB-6811</a>
</td>
                                            <td class="issuelinks">                                        PMG-42583,                                                PMG-42582                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - EXRI0000004930 - Reversal Request - MT564/seev.035 bugs 
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 14/Apr/25 3:08 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="05/May/25 1:30 PM"><time class="livestamp allow-future" datetime="2025-05-05T13:30:15+0100">05/May/25 1:30 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      EXRI0000004930
<br/>
Reversal Request (only) for BUYA Option was entered manually and validated.
<br/>

<br/>
When checking MT564 REPE / seev.035 (attached), I detected the following bugs:
<br/>

<br/>
* MT564: Function of the message should be REPE not ADDB (already handled in OEKB-6805)
<br/>
* MT564 and seev.035: all options are populated in the message, but only one (BUYA) is subject to reversal\! Is there an option to only populate the relevant option to be reversed?
<br/>
* seev.035: EXER and SLLE options contain the same &quot;Cash Movement Details&quot; which are incorrect, as those belong to the BUYA entitlement =&gt;
<br/>

<br/>
&lt;CshMvmntDtls&gt;
<br/>
&lt;CdtDbtInd&gt;CRDT&lt;/CdtDbtInd&gt;
<br/>
&lt;CshAcctId&gt;
<br/>
&lt;Prtry&gt;CATEURBKAUATWWXXXMAINACC&lt;/Prtry&gt;
<br/>
&lt;/CshAcctId&gt;
<br/>
&lt;AmtDtls&gt;
<br/>
&lt;GrssCshAmt Ccy=&quot;EUR&quot;&gt;2664&lt;/GrssCshAmt&gt;
<br/>
&lt;NetCshAmt Ccy=&quot;EUR&quot;&gt;2664&lt;/NetCshAmt&gt;
<br/>
&lt;EntitldAmt Ccy=&quot;EUR&quot;&gt;2664&lt;/EntitldAmt&gt;
<br/>
&lt;/AmtDtls&gt;
<br/>
&lt;DtDtls&gt;
<br/>
&lt;PmtDt&gt;
<br/>
&lt;DtCd&gt;
<br/>
&lt;Cd&gt;UKWN&lt;/Cd&gt;
<br/>
&lt;/DtCd&gt;
<br/>
&lt;/PmtDt&gt;
<br/>
&lt;ValDt&gt;
<br/>
&lt;Dt&gt;2025-04-14&lt;/Dt&gt;
<br/>
&lt;/ValDt&gt;
<br/>
&lt;/DtDtls&gt;
<br/>
&lt;/CshMvmntDtls&gt;
<br/>

<br/>

<br/>
Could you please check?
<br/>
Thanks, Bert
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1560910" rel="1560910" data-issuekey="OEKB-6810" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6810" href="https://jira.vermeg.com/browse/OEKB-6810">OEKB-6810</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 ASOC QAS: Number-Codes instead of error messages
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 14/Apr/25 2:36 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="14/Apr/25 5:22 PM"><time class="livestamp allow-future" datetime="2025-04-14T17:22:23+0100">14/Apr/25 5:22 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Event EXOF0000004880 (Ref. Date 23.06.2025), Account 222100
<br/>

<br/>
For this event for this account I created two instructions in MegaCor which were immediately visible in ASOC (see attachment).
<br/>

<br/>
After clicking on the Details-icon of the rejected instruction you only see number-codes instead of the real error message. In addition: Everytime I open this screen, this code could be different - so it seems this is a random code.
<br/>

<br/>
Attached you see the error messages in MegaCor.
<br/>

<br/>
I checked already with OEKB IT and got following feedback:
<br/>

<br/>
_“The following messages are delivered in the instruction search of Megacor:_
<br/>

<br/>
_2025-04-08 16:06:22,472 \[https-jsse-nio-8175-exec-10] INFO  at.oekb.casa.model.webservices.RestServiceProvider - Json-Object at.oekb.casa.swcodegen.megacor.model.InstructionSearchParams;_
<br/>

<br/>
_\{&quot;pageSize&quot;:1,&quot;client&quot;:\[\\\{&quot;value&quot;:&quot;222100&quot;}_
<br/>

<br/>
_],&quot;pageNumber&quot;:1,&quot;instrAppReference&quot;:\[\\\{&quot;value&quot;:&quot;ISCI000000001405&quot;}],&quot;forValidation&quot;:false}_
<br/>
_================_
<br/>
_2025-04-08 16:06:22,530 \[https-jsse-nio-8175-exec-10] INFO  at.oekb.casa.model.webservices.RestServiceProvider - Json-Object java.util.ArrayList;_
<br/>
_\[\{&quot;creationDate&quot;:&quot;2025-04-03 00:00:00.000+0000&quot;,&quot;source&quot;:&quot;Manual&quot;,&quot;status&quot;:&quot;Rejected&quot;,&quot;entitledIsin&quot;:&quot;IT0005218380&quot;,&quot;client&quot;:&quot;222100&quot;,&quot;appReference&quot;:&quot;ISCI000000001405&quot;,&quot;quantityType&quot;:&quot;UNIT&quot;,&quot;updateDate&quot;:&quot;2025-04-03 00:00:00.000+0000&quot;,&quot;clientReference&quot;:&quot;b03042501&quot;,&quot;instructedQuantity&quot;:300,&quot;clientDeadlineTime&quot;:&quot;2025-06-20 08:00:00.000+0000&quot;,&quot;clientDeadlineTimeZone&quot;:&quot;+02:00&quot;,&quot;mainReference&quot;:&quot;EXOF0000004880&quot;,&quot;updatorUserID&quot;:&quot;scb&quot;,&quot;creatorUserID&quot;:&quot;scb&quot;,&quot;statusDetails&quot;:&quot;Inst_Rejected&quot;,&quot;optionType&quot;:&quot;001 - Secu Option&quot;,&quot;businessError&quot;:\[_
<br/>

<br/>
_\{&quot;code&quot;:&quot;5540&quot;}_
<br/>

<br/>
_,\{&quot;code&quot;:&quot;5539&quot;}],&quot;securityAccount&quot;:&quot;222100&quot;,&quot;code&quot;:&quot;5541&quot;}]“_
<br/>

<br/>
Please check this topic as our clients need correct error messages.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1560900" rel="1560900" data-issuekey="OEKB-6809" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6809" href="https://jira.vermeg.com/browse/OEKB-6809">OEKB-6809</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 ASOC QAS: Internal Error when click on &#39;Search Message&#39;
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 14/Apr/25 2:20 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="14/Apr/25 5:18 PM"><time class="livestamp allow-future" datetime="2025-04-14T17:18:27+0100">14/Apr/25 5:18 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Event EXOF0000004880
<br/>

<br/>
after filling in an account number 222100 and the Main Reference EXOF0000004880 and tclicking on ‘Search Messages’ in ASOC, the attached error message occurs.
<br/>

<br/>
I checked already with OEKB IT and got following feedback from them:
<br/>

<br/>
_“The following search is sent to Megacor:_
<br/>

<br/>
_Json-Object at.oekb.casa.swcodegen.megacor.model.NotificationSearchParams; \{&quot;pageSize&quot;:100000,&quot;client&quot;:\[_
<br/>

<br/>
_\{&quot;value&quot;:&quot;222100&quot;}_
<br/>

<br/>
_],&quot;pageNumber&quot;:1,&quot;creationDateFrom&quot;:&quot;2025-04-01 14:12:00.000+0000&quot;,&quot;creationDateTo&quot;:&quot;2025-04-08 14:12:00.000+0000&quot;,&quot;mainReference&quot;:\[\\\{&quot;value&quot;:&quot;EXOF0000004880&quot;}]}_
<br/>

<br/>
_But no answer is received back._
<br/>

<br/>
_Error message from Logs:_
<br/>

<br/>
_at.oekb.casa.swcodegen.megacor.invoker.ApiException: com.fasterxml.jackson.databind.exc.MismatchedInputException: No content to map due to end-of-input_
<br/>

<br/>
_Please clarify with Vermeg.”_ 
<br/>

<br/>
Please check that\!
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1560892" rel="1560892" data-issuekey="OEKB-6808" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6808" href="https://jira.vermeg.com/browse/OEKB-6808">OEKB-6808</a>
</td>
                                            <td class="issuelinks">                                        PMG-42825,                                                PMG-42575,                                                OEKB-6807                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 15 - no seev.035 REVR for all entitlements/payments
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 14/Apr/25 1:43 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="15/Apr/25 4:17 PM"><time class="livestamp allow-future" datetime="2025-04-15T16:17:25+0100">15/Apr/25 4:17 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Event DVCA0000004883
<br/>

<br/>
For client 222100 three entitlements exist (1.001, 480 and 16.000 shares) due to breakdown instructions (see attachment).
<br/>

<br/>
After creating a reversal request we noticed that only for one of these three entitlements (for the 1.001 shares - the one without breakdown instruction) a seev.035 REVR has been generated - for the others not.
<br/>

<br/>
Please check that as this seems not correct in our opinion.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1560831" rel="1560831" data-issuekey="OEKB-6807" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6807" href="https://jira.vermeg.com/browse/OEKB-6807">OEKB-6807</a>
</td>
                                            <td class="issuelinks">                                        OEKB-6808                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 15 - ADTX missing on outgoing seev.035
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 14/Apr/25 11:43 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="16/Apr/25 11:14 AM"><time class="livestamp allow-future" datetime="2025-04-16T11:14:20+0100">16/Apr/25 11:14 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Event DVCA0000004883
<br/>

<br/>
on all outgoing seev.035 the outgoing comment was missing. On seev.031 it was included. You find all messages attached.
<br/>

<br/>
We noticed that also for these holdings with breakdown instructions (one for 16.000 shs and one for 480 shs), the seev.035 do not have the standard text as for ISO 15022 messages - see attached an examples.
<br/>

<br/>
Please check that the outgoing comments and the texts for breakdown instructions are overtaken into the outgoing ISO20022 messages for all events.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1560424" rel="1560424" data-issuekey="OEKB-6805" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6805" href="https://jira.vermeg.com/browse/OEKB-6805">OEKB-6805</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - RHDI0000004759 - MT564 Reversal Notification - :23G:: should be REPE (not ADDB)
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Nadia BOUSSETTA
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 11/Apr/25 3:54 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="28/Apr/25 9:54 AM"><time class="livestamp allow-future" datetime="2025-04-28T09:54:56+0100">28/Apr/25 9:54 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 28/Apr/25 9:38 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      RHDI0000004759
<br/>
When checking MT564 Reversal Notification (attached), we noticed that the Message Function (:23G::) is ADDB.
<br/>

<br/>
This doesn&#39;t comply with the SMPG Global Market Practice guide (see screenshot).
<br/>

<br/>
Could you please check and amend the Message Function to REPE?
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1559729" rel="1559729" data-issuekey="OEKB-6802" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6802" href="https://jira.vermeg.com/browse/OEKB-6802">OEKB-6802</a>
</td>
                                            <td class="issuelinks">                                        PMG-42653                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - EXRI0000004930 - seev.036 OUT BUYA/SLLE Option - Mapping errors
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 10/Apr/25 11:16 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663"></td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      EXRI0000004930
<br/>
When checking seev.036 for EXRI BUYA/SLLE option (attached), I detected the following mapping issues:
<br/>

<br/>
* Broker Price is missing
<br/>
(this bug also relates to MT566 OUT, see OEKB-6801)
<br/>

<br/>
* Trade Date/Time is missing
<br/>
(see, in comparison, MT566 OUT:
<br/>
:13A::CAON//002
<br/>
:22F::CAOP//SLLE
<br/>
:98C::TRAD//20250409104700)
<br/>

<br/>
* RateDtls (SLLE Option) should be skipped:
<br/>
&lt;RateDtls&gt;
<br/>
&lt;AddtlQtyForExstgScties&gt;
<br/>
&lt;QtyToQty&gt;
<br/>
&lt;Qty1&gt;1&lt;/Qty1&gt;
<br/>
&lt;Qty2&gt;1&lt;/Qty2&gt;
<br/>
&lt;/QtyToQty&gt;
<br/>
&lt;/AddtlQtyForExstgScties&gt;
<br/>
&lt;/RateDtls&gt;
<br/>

<br/>

<br/>
In addition, there are the same mapping issues as decribed in OEKB-6797.
<br/>

<br/>
Could you please check?
<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1559647" rel="1559647" data-issuekey="OEKB-6801" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6801" href="https://jira.vermeg.com/browse/OEKB-6801">OEKB-6801</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 QAS - EXRI0000004930 - MT566 for BUYA/SLLE doesn&#39;t include Broker Price
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 10/Apr/25 10:17 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="28/Apr/25 9:57 AM"><time class="livestamp allow-future" datetime="2025-04-28T09:57:26+0100">28/Apr/25 9:57 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 28/Apr/25 9:39 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      EXRI0000004930
<br/>
When checking MT566 OUT for EXRI event BUYA/SLLE option (attached), I noticed that the Broker Price is missing.
<br/>

<br/>
In comparison, see MT566 OUT generated in M10 =&gt;
<br/>

<br/>
:13A::CAON//003
<br/>
:22F::CAOP//*BUYA*
<br/>
:98C::TRAD//20230626113651
<br/>
:16R:CASHMOVE
<br/>
:22H::CRDB//DEBT
<br/>
:97A::CASH//CATEURRZBAATWWXXXOEKB
<br/>
:19B::PSTA//EUR39,68
<br/>
:19B::NETT//EUR39,68
<br/>
:98A::POST//20230628
<br/>
:98A::VALU//20230628
<br/>
*:90B::PRPP//ACTU/EUR6,61333*
<br/>

<br/>
:13A::CAON//002
<br/>
:22F::CAOP//*SLLE*
<br/>
:98C::TRAD//20230623113004
<br/>
:16R:SECMOVE
<br/>
:22H::CRDB//DEBT
<br/>
:35B:ISIN AT0000A35PJ0
<br/>
BR A/LENZING AKT O.N.
<br/>
:36B::PSTA//UNIT/64147,
<br/>
:98A::POST//20230627
<br/>
:16S:SECMOVE
<br/>
:16R:CASHMOVE
<br/>
:22H::CRDB//CRED
<br/>
:97A::CASH//CDEEURFMBKDEMMGRPCBDCLIENTFR
<br/>
:19B::PSTA//EUR486542,17
<br/>
:19B::NETT//EUR486542,17
<br/>
:98A::POST//20230627
<br/>
:98A::VALU//20230627
<br/>
*:90B::OFFR//ACTU/EUR7,5848*
<br/>

<br/>

<br/>
Could you please check and make sure that the mapping will be amended also in PROD?
<br/>

<br/>
Thanks, Bert
<br/>

<br/>

<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1559459" rel="1559459" data-issuekey="OEKB-6797" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6797" href="https://jira.vermeg.com/browse/OEKB-6797">OEKB-6797</a>
</td>
                                            <td class="issuelinks">                                        PMG-42652                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - EXRI0000004930 - seev.036 OUT - Mapping errors
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 09/Apr/25 2:59 PM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="09/Apr/25 3:06 PM"><time class="livestamp allow-future" datetime="2025-04-09T15:06:48+0100">09/Apr/25 3:06 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      EXRI0000004930
<br/>
When checking seev.036 OUT (attached), I noticed the following:
<br/>

<br/>
* linkage to the client instruction reference is missing
<br/>
(see, in comparison, MT566 OUT:
<br/>
:13A::LINK//565
<br/>
:20C::RELA//eg0904202501
<br/>
:16S:LINK)
<br/>

<br/>
* cash account for both main payment and fee debit missing
<br/>
(see, in comparision, MT566 OUT:
<br/>
:97A::CASH//CATEURGIBAATWGXXXPROD
<br/>
:97A::CASH//***********)
<br/>

<br/>
* Main payment: subscription price is populated as &quot;Gross Dividend Rate&quot;:
<br/>
&lt;GrssDvddRate&gt;
<br/>
&lt;Amt Ccy=&quot;EUR&quot;&gt;3.17&lt;/Amt&gt;
<br/>
&lt;/GrssDvddRate&gt;
<br/>

<br/>
I guess, &quot;Generic Cash Price Paid Per Product&quot; must be used here?
<br/>

<br/>
* Fee payment: subscription price is populated also in this sequence (see screenshot) and should be skipped, no information available about fee debit, see in comparison MT566 OUT:
<br/>
:19B::CHAR//EUR10,
<br/>

<br/>
Could you please check?
<br/>
Thanks, Bert
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1559411" rel="1559411" data-issuekey="OEKB-6795" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6795" href="https://jira.vermeg.com/browse/OEKB-6795">OEKB-6795</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - EXRI0000004930 - seev.034 OUT - LINK to instruction received missing
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Nadia BOUSSETTA
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 09/Apr/25 11:20 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="09/Apr/25 11:34 AM"><time class="livestamp allow-future" datetime="2025-04-09T11:34:07+0100">09/Apr/25 11:34 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 28/Apr/25 9:37 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      EXRI0000004930
<br/>
When checking seev.034 OUT, I noticed that there is no LINK to the instruction received from the client, like it is available in MT567 OUT =&gt;
<br/>

<br/>
:16R:LINK
<br/>
:13A::LINK//565
<br/>
:20C::RELA//eg0904202501
<br/>

<br/>
Could you please check how to populate the related instruction reference also in seev.034 OUT?
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1559408" rel="1559408" data-issuekey="OEKB-6794" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6794" href="https://jira.vermeg.com/browse/OEKB-6794">OEKB-6794</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - EXRI0000004930 - seev.034 OUT - &lt;SfkpgAcct&gt; must include Prefix &#39;OCSD&#39;
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Nadia BOUSSETTA
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 09/Apr/25 11:07 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="09/Apr/25 11:12 AM"><time class="livestamp allow-future" datetime="2025-04-09T11:12:39+0100">09/Apr/25 11:12 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 28/Apr/25 9:36 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      EXRI0000004930
<br/>
When checking seev.034 OUT (attached), I noticed that &lt;SfkpgAcct&gt; is populated without the Prefix &#39;OCSD&#39;.
<br/>

<br/>
Please make sure that &lt;SfkpgAcct&gt; includes &#39;OCSD&#39; in every outgoing message.
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1559182" rel="1559182" data-issuekey="OEKB-6792" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6792" href="https://jira.vermeg.com/browse/OEKB-6792">OEKB-6792</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 QAS - EXRI0000004930 - BUYA/SLLE ONGO not saved
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 08/Apr/25 2:55 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="08/Apr/25 4:32 PM"><time class="livestamp allow-future" datetime="2025-04-08T16:32:19+0100">08/Apr/25 4:32 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 28/Apr/25 9:40 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      EXRI0000004930
<br/>
Pay Date of Cash Movement in SLLE and BUYA Option was set to ONGO (screenshot).
<br/>

<br/>
However, after validating the update, the flag is still set to false (screenshot 2).
<br/>

<br/>
Could you please check and also make sure that this bug is fixed also in PROD?\!
<br/>
BR Bert
  </td>
                    </tr>


                <tr id="issuerow1559159" rel="1559159" data-issuekey="OEKB-6791" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6791" href="https://jira.vermeg.com/browse/OEKB-6791">OEKB-6791</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - EXRI0000004930 - seev.031 OUT - &lt;EndDt&gt; incorrect /to be skipped in BUYA and SLLE Option
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 08/Apr/25 2:34 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="28/Apr/25 9:53 AM"><time class="livestamp allow-future" datetime="2025-04-28T09:53:23+0100">28/Apr/25 9:53 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 28/Apr/25 9:39 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      EXRI0000004930
<br/>
When checking correctness of seev.031 OUT via SWIFT-testing tool, the below errors were received.
<br/>

<br/>
Please note that the &quot;Election End&quot; should be skipped for BUYA and SLLE in seev.031 OUT anyway, as it is only a technical field (we do not enter this value). Only response deadline must be populated, as it is in MT564 OUT =&gt;
<br/>

<br/>
:13A::CAON//002
<br/>
:22F::CAOP//SLLE
<br/>
:11A::OPTN//EUR
<br/>
:17B::DFLT//N
<br/>
:98E::RDDT//20250408100000/02
<br/>
:16R:SECMOVE
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1557792" rel="1557792" data-issuekey="OEKB-6781" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6781" href="https://jira.vermeg.com/browse/OEKB-6781">OEKB-6781</a>
</td>
                                            <td class="issuelinks">                                        PMG-42577                        </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 15 - Receiver BIC not available
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 03/Apr/25 11:33 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="03/Apr/25 11:51 AM"><time class="livestamp allow-future" datetime="2025-04-03T11:51:06+0100">03/Apr/25 11:51 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Event DVCA0000004883
<br/>

<br/>
we sent notifications to all clients of this event and noticed that for 222100 which receives ISO20022 messages, the Receiver BIC is not filled in in MegaCor:
<br/>

<br/>
Is this only a cosmetical topic or are there problems because of that?
<br/>

<br/>
Please check that,
<br/>

<br/>
thanks, stefan
  </td>
                    </tr>


                <tr id="issuerow1557773" rel="1557773" data-issuekey="OEKB-6780" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6780" href="https://jira.vermeg.com/browse/OEKB-6780">OEKB-6780</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Minor
    </td>
                                            <td class="summary"><p>
                M12 QAS: HIS Rule Error twice
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 03/Apr/25 11:12 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="03/Apr/25 3:21 PM"><time class="livestamp allow-future" datetime="2025-04-03T15:21:27+0100">03/Apr/25 3:21 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Event EXOF0000004880
<br/>

<br/>
Today we created instructions for this event and one has been rejected immediately due to following error(s).
<br/>

<br/>
Can you tell us why this error message is twice here?
<br/>

<br/>
As these error messages are also forwarded to the Asset Servicing Online Client, the OCSD-Clients see this message also twice.
<br/>

<br/>
Please ensure in general that error messages are not shown duplicated in the system.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1557669" rel="1557669" data-issuekey="OEKB-6779" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6779" href="https://jira.vermeg.com/browse/OEKB-6779">OEKB-6779</a>
</td>
                                            <td class="issuelinks">                                        OEKB-6755                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - EXOF0000004880 - seev.031 OUT - &lt;FrctnDspstn&gt; incorrect
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 03/Apr/25 9:43 AM </td>
                                            <td class="fixVersions">                        SCORE Patch_03042025            </td>
                                            <td class="customfield_12663">            <span title="03/Apr/25 1:04 PM"><time class="livestamp allow-future" datetime="2025-04-03T13:04:24+0100">03/Apr/25 1:04 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 03/Apr/25 6:46 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      EXOF0000004880
<br/>
was set up with SECU IN Rounding In = DOWN (screenshot).
<br/>

<br/>
However, in seev.031 OUT (attached),
<br/>
 &lt;FrctnDspstn&gt; is mapped incorrectly =&gt;
<br/>

<br/>
&lt;OptnNb&gt;001&lt;/OptnNb&gt;
<br/>
&lt;OptnTp&gt;
<br/>
&lt;Cd&gt;SECU&lt;/Cd&gt;
<br/>
&lt;/OptnTp&gt;
<br/>
&lt;FrctnDspstn&gt;
<br/>
*&lt;Cd&gt;DIST&lt;/Cd&gt;*
<br/>
&lt;/FrctnDspstn&gt;
<br/>

<br/>
Please check\!
  </td>
                    </tr>


                <tr id="issuerow1557661" rel="1557661" data-issuekey="OEKB-6778" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6778" href="https://jira.vermeg.com/browse/OEKB-6778">OEKB-6778</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 QAS - EXOF0000004880 - Client Entitlements incorrect (also PRODUCTION issue?)
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;In Progress&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;indicating that the improvement  is being analyzed, This status indicates also that the improvement can need more elements and can be assigned to another person.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;In Progress&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;indicating that the improvement  is being analyzed, This status indicates also that the improvement can need more elements and can be assigned to another person.&lt;/span&gt;">In Progress</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 03/Apr/25 9:32 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="03/Apr/25 4:09 PM"><time class="livestamp allow-future" datetime="2025-04-03T16:09:53+0100">03/Apr/25 4:09 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      EXOF0000004880
<br/>
SECU IN was set up with Rounding In = DOWN, Fraction Movement is not filled.
<br/>
Market Entitlements were calculated correctly (SECU IN quantity was rounded down), see screenshot 1.
<br/>

<br/>
However, Client Entitlements were calculated incorrectly (SECU IN quantity contains fractions), see screenshot 2.
<br/>

<br/>
Could you please check if this bug also exists in PRODUCTION environment, as I was testing a production event in QAS\!
<br/>

<br/>

<br/>

<br/>
Thanks, Bert
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1557073" rel="1557073" data-issuekey="OEKB-6770" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6770" href="https://jira.vermeg.com/browse/OEKB-6770">OEKB-6770</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 15 - seev.031 for PROX event
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Nadia BOUSSETTA
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 01/Apr/25 11:09 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="01/Apr/25 1:43 PM"><time class="livestamp allow-future" datetime="2025-04-01T13:43:47+0100">01/Apr/25 1:43 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 28/Apr/25 9:37 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi Emna,
<br/>

<br/>
I am afraid we have to overthink the logic of all the SLAs we created for OENB (and in the meantime also for 222100) for sending ISO20022.
<br/>

<br/>
With the current configuration, the new SLAs have a higher weight and the template seev.031 is also taken for PROX events. I assume this would also happen for SHDS but I couldn&#39;t send a notification for that (see ticket OEKB-6769).
<br/>

<br/>
I assume we need more SLAs than the 19 already created to ensure that for SRD II events the correct seev.message-templates are used.
<br/>

<br/>
Do you deliver a suggest what is needed or should we set up a call to think and discuss about that together to avoid wrong message-flow in the future? If so, please send me your possible timeslots.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1556672" rel="1556672" data-issuekey="OEKB-6765" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6765" href="https://jira.vermeg.com/browse/OEKB-6765">OEKB-6765</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 15 - outgoing seev.033 instruction comment not mapped
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 28/Mar/25 11:59 AM </td>
                                            <td class="fixVersions">                        SCORE Patch_03042025            </td>
                                            <td class="customfield_12663">            <span title="03/Apr/25 6:43 PM"><time class="livestamp allow-future" datetime="2025-04-03T18:43:11+0100">03/Apr/25 6:43 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 03/Apr/25 6:43 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      event BIDS0000004814
<br/>

<br/>
I consolidated the client instruction received via seev.033 and claimed for not mapping the instruction comment in OEKB-6764 and created the market instruction.
<br/>

<br/>
After editing this MI and adding an INST comment I sent out the MI to the custodian. In the outgoing MT565 everything looks fine. In the seev.033 the instruction comment is again not mapped and not included.
<br/>

<br/>
I attached both sent MI for you to this bug.
<br/>

<br/>
Please check the problem and fix it.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1556661" rel="1556661" data-issuekey="OEKB-6764" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6764" href="https://jira.vermeg.com/browse/OEKB-6764">OEKB-6764</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 15 - incoming seev.033 - instruction comment not mapped
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 28/Mar/25 11:44 AM </td>
                                            <td class="fixVersions">                        SCORE Patch_03042025            </td>
                                            <td class="customfield_12663">            <span title="03/Apr/25 6:43 PM"><time class="livestamp allow-future" datetime="2025-04-03T18:43:42+0100">03/Apr/25 6:43 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 03/Apr/25 6:43 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      event BIDS0000004814
<br/>

<br/>
I instructed for above event with attached seev.033 instruction but unfortunately it got not Status InvalidData due to the comment but got Status Created (after the WaitingFreeze).
<br/>

<br/>
This is wrong - the logic must be implemented as in ISO15022 where this already works (see other attached instruction).
<br/>

<br/>
Please adapt that.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1556608" rel="1556608" data-issuekey="OEKB-6763" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6763" href="https://jira.vermeg.com/browse/OEKB-6763">OEKB-6763</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - RHDI0000004759 - seev.036 OUT - &quot;Intermediate Securities Distribution Type&quot; missing
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 28/Mar/25 10:07 AM </td>
                                            <td class="fixVersions">                        SCORE Patch_03042025            </td>
                                            <td class="customfield_12663">            <span title="28/Mar/25 10:36 AM"><time class="livestamp allow-future" datetime="2025-03-28T10:36:56+0000">28/Mar/25 10:36 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 03/Apr/25 6:44 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      RHDI0000004759
<br/>
When checking seev.036 (attached), I noticed that the &quot;Intermediate Securities Distribution Type&quot; is missing under &quot;Corporate Action Details&quot;.
<br/>
I guess this must be populated under
<br/>
/Document/CorpActnMvmntConf/CorpActnDtls/IntrmdtSctiesDstrbtnTp
<br/>

<br/>
For comparison, see MT564 =&gt;
<br/>
:16R:CADETL
<br/>
:98A::XDTE//20250318
<br/>
:98A::RDTE//20250319
<br/>
*:22F::RHDI//EXRI*
<br/>
:16S:CADETL
<br/>

<br/>
Could you please check?
<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1556578" rel="1556578" data-issuekey="OEKB-6762" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6762" href="https://jira.vermeg.com/browse/OEKB-6762">OEKB-6762</a>
</td>
                                            <td class="issuelinks">                                        PMG-42469                        </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 QAS: Repair invalid instruction not possible
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Thouraya BEN ABDALLAH
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 28/Mar/25 8:37 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="28/Mar/25 12:59 PM"><time class="livestamp allow-future" datetime="2025-03-28T12:59:32+0000">28/Mar/25 12:59 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 07/Apr/25 5:59 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Event BIDS0000004814
<br/>

<br/>
Instruction MT565 with text has been received and got in Status InvalidData - which is correct.
<br/>

<br/>
When repairing the instruction I wanted to open the instruction comment (which was visible twice - maybe you could check that too) I got following error message.
<br/>

<br/>
Nevertheless I could deactivate the Narrative check field and could repair the instruction.
<br/>

<br/>
Please check what is the problem with that error message.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1556437" rel="1556437" data-issuekey="OEKB-6760" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6760" href="https://jira.vermeg.com/browse/OEKB-6760">OEKB-6760</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - EXRI0000004760 - seev.031 OUT - &lt;AddtlInf&gt; contains strange characters
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Hafedh BEN SASSI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 27/Mar/25 3:09 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="02/Apr/25 2:06 PM"><time class="livestamp allow-future" datetime="2025-04-02T14:06:57+0100">02/Apr/25 2:06 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      EXRI0000004760
<br/>
When checking Additional Info, I noticed that there are lots of strange characters (see screenshot). 
<br/>
Is this only a display issue or will these characters be available in the message?
<br/>

<br/>
Could you please check?
<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1556432" rel="1556432" data-issuekey="OEKB-6759" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6759" href="https://jira.vermeg.com/browse/OEKB-6759">OEKB-6759</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - EXRI0000004760 - seev.031 OUT - SLLE/BUYA Option - Mapping errors
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 27/Mar/25 3:02 PM </td>
                                            <td class="fixVersions">                        SCORE Patch_03042025            </td>
                                            <td class="customfield_12663">            <span title="03/Apr/25 6:44 PM"><time class="livestamp allow-future" datetime="2025-04-03T18:44:41+0100">03/Apr/25 6:44 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 03/Apr/25 6:44 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      EXRI0000004760
<br/>
Could you please check and correct the following mapping errors =&gt;
<br/>

<br/>
SLLE/BUYA Option:
<br/>

<br/>
* Please skip Market Deadline as there is no such value available =&gt;
<br/>
&lt;MktDdln&gt;
<br/>
&lt;DtCd&gt;
<br/>
&lt;Cd&gt;UKWN&lt;/Cd&gt;
<br/>
&lt;/DtCd&gt;
<br/>
&lt;/MktDdln&gt;
<br/>

<br/>
BUYA Option:
<br/>

<br/>
* &lt;CshMvmntDtls&gt;
<br/>
&lt;CdtDbtInd&gt;CRDT&lt;/CdtDbtInd&gt; is incorrect (must be DBIT)
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1556389" rel="1556389" data-issuekey="OEKB-6758" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6758" href="https://jira.vermeg.com/browse/OEKB-6758">OEKB-6758</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - EXRI0000004760 - seev.031 OUT - EXER Option - &lt;CshMvmntDtls&gt; incorrect/incomplete
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 27/Mar/25 2:48 PM </td>
                                            <td class="fixVersions">                        SCORE Patch_03042025            </td>
                                            <td class="customfield_12663">            <span title="03/Apr/25 6:45 PM"><time class="livestamp allow-future" datetime="2025-04-03T18:45:06+0100">03/Apr/25 6:45 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 03/Apr/25 6:44 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      EXRI0000004760
<br/>
Please check &lt;CshMvmntDtls&gt; in EXER option =&gt;
<br/>

<br/>
* &lt;CdtDbtInd&gt; CRDT is incorrect (must be DBIT)
<br/>
* Exercise Price is missing
<br/>

<br/>
For comparison, see MT564 =&gt;
<br/>
:16R:CASHMOVE
<br/>
:22H::CRDB//DEBT
<br/>
:98A::PAYD//20250324
<br/>
:90B::PRPP//ACTU/EUR3,17
<br/>
:16S:CASHMOVE
<br/>

<br/>
Both seev.031 and MT564 attached.
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1556375" rel="1556375" data-issuekey="OEKB-6757" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6757" href="https://jira.vermeg.com/browse/OEKB-6757">OEKB-6757</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - EXRI0000004760 - seev.031 OUT should not include &lt;RcrdDt&gt; UKWN if there is no Record Date
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 27/Mar/25 2:33 PM </td>
                                            <td class="fixVersions">                        SCORE Patch_03042025            </td>
                                            <td class="customfield_12663">            <span title="03/Apr/25 6:47 PM"><time class="livestamp allow-future" datetime="2025-04-03T18:47:03+0100">03/Apr/25 6:47 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 03/Apr/25 6:47 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      EXRI0000004760
<br/>
When checking seev.031 OUT (attached), I noticed that Record Date is populated as unknown =&gt;
<br/>

<br/>
&lt;RcrdDt&gt;
<br/>
&lt;DtCd&gt;
<br/>
&lt;Cd&gt;UKWN&lt;/Cd&gt;
<br/>
&lt;/DtCd&gt;
<br/>
&lt;/RcrdDt&gt;
<br/>

<br/>
As there is no Record Date at all, this field should be skipped in the message.
<br/>

<br/>
Please check\!
<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1556361" rel="1556361" data-issuekey="OEKB-6756" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6756" href="https://jira.vermeg.com/browse/OEKB-6756">OEKB-6756</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Critical
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 15 - incoming seev.031 not totally mapped into MegaCor
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 27/Mar/25 2:01 PM </td>
                                            <td class="fixVersions">                        SCORE Patch_03042025            </td>
                                            <td class="customfield_12663">            <span title="27/Mar/25 2:05 PM"><time class="livestamp allow-future" datetime="2025-03-27T14:05:03+0000">27/Mar/25 2:05 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 03/Apr/25 6:47 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Today I created BIDS event BIDS0000004814 via incoming SWIFT seev.031 from CBF.
<br/>

<br/>
Unfortunately much information was not mapped into MegaCor although value was included in the seev.031.
<br/>

<br/>
You find the SWIFT seev.031 attached and also screens of the event in MegaCor
<br/>

<br/>
Following fields have no value in MegaCor but should have a value:
<br/>

<br/>
* Comments for Notification: OFFO
<br/>
* Comments for Notification: WEBB
<br/>
* Option 001 and 002: Option Feature: BOIS
<br/>
* Option 001 and 002: Instruction Processing Info: MIEX and MILT
<br/>
* Option 001: SEC OUT: ONGO must be YES
<br/>
* Option 001 and 002: CASH IN: Price
<br/>
* Option 001 and 002 and 003: Option Comment: INCO
<br/>
* Event Incoming Comment: ADTX
<br/>
* Event Incoming Comment: COMP
<br/>

<br/>
Please check these fields and make sure that everything which is included in the incoming seev.031 is mapped, as it is also for incoming MT564 SWIFTs.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1556339" rel="1556339" data-issuekey="OEKB-6755" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6755" href="https://jira.vermeg.com/browse/OEKB-6755">OEKB-6755</a>
</td>
                                            <td class="issuelinks">                                        OEKB-6779                        </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 15 - Fraction Disposition should not be included
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 27/Mar/25 1:17 PM </td>
                                            <td class="fixVersions">                        SCORE Patch_03042025            </td>
                                            <td class="customfield_12663">            <span title="03/Apr/25 6:45 PM"><time class="livestamp allow-future" datetime="2025-04-03T18:45:42+0100">03/Apr/25 6:45 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 03/Apr/25 6:45 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Event BIDS0000004813 (Superevent BIDS).
<br/>

<br/>
In the outgoing seev.031 I found the following part in option 001/CASH and 002/CASH:
<br/>

<br/>
_&lt;FrctnDspstn&gt;_
<br/>
         _&lt;Cd&gt;DIST&lt;/Cd&gt;_
<br/>
_&lt;/FrctnDspstn&gt;_
<br/>

<br/>
This part is not included in the outgoing MT564.
<br/>

<br/>
As there is no SECU IN part in these two options this tag shouldn&#39;t be included in the outgoing seev.031.
<br/>

<br/>
Please adapt the ISO20022-behaviour according to the ISO15022-behaviour.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1556335" rel="1556335" data-issuekey="OEKB-6754" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6754" href="https://jira.vermeg.com/browse/OEKB-6754">OEKB-6754</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 15 - no UKWN-deadlines when no deadlines existing
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 27/Mar/25 1:04 PM </td>
                                            <td class="fixVersions">                        SCORE Patch_03042025            </td>
                                            <td class="customfield_12663">            <span title="03/Apr/25 6:48 PM"><time class="livestamp allow-future" datetime="2025-04-03T18:48:04+0100">03/Apr/25 6:48 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 03/Apr/25 6:48 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      At event BIDS0000004813 (Superevent as BIDS) there is option 003/NOAC. No deadlines are included in MegaCor (see attachment). But in the outgoing seev.031 there are parts for both market and client deadline with value UKWN. In the MT564 there are no lines for these dates.
<br/>

<br/>
Attached you find the ISO15022 and ISO20022 message for this event.
<br/>

<br/>
Please adapt ISO20022 according to the behaviour for ISO15022 - if there are no deadlines, no UKWN-dates are needed.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1556330" rel="1556330" data-issuekey="OEKB-6753" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6753" href="https://jira.vermeg.com/browse/OEKB-6753">OEKB-6753</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Minor
    </td>
                                            <td class="summary"><p>
                M12 QAS: Certification Breakdown Type not visible
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 27/Mar/25 12:52 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663"></td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      We noticed that the Certification Breakdown Type is not visible in the view screen of an event and in the update screens it is visible sometimes and sometimes not. We checked it in different events (BIDS0000004813, BIDS0000004814).
<br/>

<br/>
Attached you find the view screen of both events which look the same. But you also see the difference in the update-screens.
<br/>

<br/>
Please correct that to see this field in the normal view screen and also in the update mode. In outgoing SWIFTs this field is included.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1556321" rel="1556321" data-issuekey="OEKB-6752" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6752" href="https://jira.vermeg.com/browse/OEKB-6752">OEKB-6752</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 QAS: SEC OUT not visible in Superevent BIDS
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 27/Mar/25 12:39 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663"></td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Although there is definitively a SEC OUT section included in both CASH options of the following two events: BIDS0000004812 and BIDS0000004813 they are not shown in the VIEW screen (see attachment). Only in the update screen (second screenshot) and they are also included in the outgoing messages to the clients (you find examples attached).
<br/>

<br/>
Why are these SEC OUT sections not visible in the view screen? A refresh and also close and reopen of MegaCor didn’t help.
<br/>

<br/>
Please check that.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1556227" rel="1556227" data-issuekey="OEKB-6751" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6751" href="https://jira.vermeg.com/browse/OEKB-6751">OEKB-6751</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 15 - seev.031 generation failed
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-inprogress jira-issue-status-lozenge-indeterminate aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Pending&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The improvement is Accepted and waiting to be analyzed.&lt;/span&gt;">Pending</span>    </td>
                                            <td class="assignee">            Nadia BOUSSETTA
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 27/Mar/25 9:44 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663"></td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Event BIDS0000004812
<br/>

<br/>
Event has an ADTX outgoing comment with more than 8000 characters. For all ISO15022 messages MegaCor creates as many 70E//ADTX blocks as needed but for ISO20022 the generation has failed in MegaBroker:
<br/>

<br/>
Please make sure that also longer text can be sent via seev messages.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1556040" rel="1556040" data-issuekey="OEKB-6748" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6748" href="https://jira.vermeg.com/browse/OEKB-6748">OEKB-6748</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 15 - FAMT instead of UNIT
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 26/Mar/25 3:33 PM </td>
                                            <td class="fixVersions">                        SCORE Patch_03042025            </td>
                                            <td class="customfield_12663">            <span title="27/Mar/25 8:34 AM"><time class="livestamp allow-future" datetime="2025-03-27T08:34:19+0000">27/Mar/25 8:34 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 03/Apr/25 6:48 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Today I sent out a market instruction to custodian CBF. According to Custodian Reporting SLA both messages MT565 and seev.033 were generated.
<br/>

<br/>
Unfortunately in the outgoing seev.033 MegaCor created the SWIFT with FACE AMOUNT instead of UNIT. In MT565 it correctly took UNIT.
<br/>

<br/>
Please correct that.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1556038" rel="1556038" data-issuekey="OEKB-6747" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6747" href="https://jira.vermeg.com/browse/OEKB-6747">OEKB-6747</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 15 - OCSD-Prefix-Logic for seev.033
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 26/Mar/25 3:28 PM </td>
                                            <td class="fixVersions">                        SCORE Patch_03042025            </td>
                                            <td class="customfield_12663">            <span title="27/Mar/25 9:28 AM"><time class="livestamp allow-future" datetime="2025-03-27T09:28:53+0000">27/Mar/25 9:28 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 03/Apr/25 6:49 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Today I created event TEND0000004807 and sent in two instructions (you find them attached). The difference of them: one was sent for account OCSD205400 and the other for 205400.
<br/>

<br/>
Unfortunately MegaCor rejected the instruction with OCSD205400 and created it for 205400 but the behaviour should be exactly the opposite: We expect from clients OCSD205400 and without prefix, the instruction should be rejected.
<br/>

<br/>
Please implement this logic for ISO20022 as it already works for ISO15022.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1555499" rel="1555499" data-issuekey="OEKB-6745" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6745" href="https://jira.vermeg.com/browse/OEKB-6745">OEKB-6745</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - BONU0000004763 - seev.036 OUT - Cash In Lieu Of Share Price missing
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 26/Mar/25 8:32 AM </td>
                                            <td class="fixVersions">                        SCORE Patch_03042025            </td>
                                            <td class="customfield_12663">            <span title="26/Mar/25 9:20 AM"><time class="livestamp allow-future" datetime="2025-03-26T09:20:10+0000">26/Mar/25 9:20 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 03/Apr/25 6:49 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      BONU0000004763
<br/>
When checking seev.036 OUT (attached), I noticed that there is no &quot;Cash In Lieu Of Share Price&quot; available.
<br/>
In the MT566 OUT, this is populated as follows =&gt;
<br/>

<br/>
:90B::CINL//ACTU/EUR48,55
<br/>

<br/>
I suppose, in the seev.036 OUT, the relevant field is =&gt;
<br/>

<br/>
/Document/CorpActnMvmntConf/CorpActnConfDtls/PricDtls/CshInLieuOfShrPric
<br/>

<br/>
Could you please check?
<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1553865" rel="1553865" data-issuekey="OEKB-6737" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6737" href="https://jira.vermeg.com/browse/OEKB-6737">OEKB-6737</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - EXRI0000004760 - Generation of seev.031 failed
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 20/Mar/25 11:31 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="21/Mar/25 11:51 AM"><time class="livestamp allow-future" datetime="2025-03-21T11:51:32+0000">21/Mar/25 11:51 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 25/Mar/25 3:22 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      EXRI0000004760
<br/>
I tried to send seev.031 for client 229500.
<br/>

<br/>
However, the generation failed with error message =&gt;
<br/>
&quot;Cannot invoke &quot;\[B.clone()&quot; because &quot;content&quot; is null&quot;
<br/>

<br/>
I guess the issue is again related to the outgoing comment as reported in ticket OEKB-6710.
<br/>
Could you please make sure to fix it for all type of events?
<br/>

<br/>
Thanks, Bert
<br/>

  </td>
                    </tr>


                <tr id="issuerow1553849" rel="1553849" data-issuekey="OEKB-6736" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6736" href="https://jira.vermeg.com/browse/OEKB-6736">OEKB-6736</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - RHDI0000004759 - seev.031 OUT - &lt;FrctnDspstn&gt; not included
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 20/Mar/25 10:22 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663"></td>
                                            <td class="resolutiondate"> 25/Mar/25 4:25 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      RHDI0000004759
<br/>
I checked seev.031 OUT and noticed that the tag &lt;FrctnDspstn&gt; is not included in the message.
<br/>
In this case, ADEX is 1 for 1, but in MT564 we still have
<br/>
:22F::DISF//DIST
<br/>
:92D::ADEX//1,/1,
<br/>
in the outgoing 564.
<br/>

<br/>
Could you please check if &lt;FrctnDspstn&gt; should be available in seev.031, even if &lt;QtyToQty&gt; = 1 for 1?
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1553845" rel="1553845" data-issuekey="OEKB-6734" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6734" href="https://jira.vermeg.com/browse/OEKB-6734">OEKB-6734</a>
</td>
                                            <td class="issuelinks">                                        PMG-42504                        </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 QAS - Create CA / Input SPLR MAND failed
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 20/Mar/25 10:09 AM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="20/Mar/25 10:21 AM"><time class="livestamp allow-future" datetime="2025-03-20T10:21:38+0000">20/Mar/25 10:21 AM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      Hi,
<br/>
I want to create SPLR manually via “Input CA”.
<br/>
After entering all data and trying to save the following error popped up (see screenshot):
<br/>
PALM-01371: The value (SPLR) for the field (Ca Code) for the entity ([com.vermeg.mcca.CA|http://com.vermeg.mcca.CA]) of code (SPLR0000004762) is not valid
<br/>

<br/>
Also, I see that the ca code SPLR was not assigned to the input screen and cannot be added, I guess that’s why the event cannot be saved.
<br/>

<br/>
Please check\!
<br/>
Thanks, Bert 
  </td>
                    </tr>


                <tr id="issuerow1552809" rel="1552809" data-issuekey="OEKB-6725" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6725" href="https://jira.vermeg.com/browse/OEKB-6725">OEKB-6725</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - TEND0000004716 - seev.031 - NOAC Option - Option Currency to be skipped
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 17/Mar/25 3:15 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="17/Mar/25 3:28 PM"><time class="livestamp allow-future" datetime="2025-03-17T15:28:31+0000">17/Mar/25 3:28 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 25/Mar/25 3:23 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      TEND0000004716
<br/>
When checking seev.031 OUT (attached), I noticed that currency option (CcyOptn) is populated under NOAC Option =&gt;
<br/>

<br/>
&lt;CorpActnOptnDtls&gt;
<br/>
&lt;OptnNb&gt;002&lt;/OptnNb&gt;
<br/>
&lt;OptnTp&gt;
<br/>
&lt;Cd&gt;NOAC&lt;/Cd&gt;
<br/>
&lt;/OptnTp&gt;
<br/>
&lt;CcyOptn&gt;EUR&lt;/CcyOptn&gt;
<br/>

<br/>
Could you please skip CcyOptn for options where no cash payment will take place.
<br/>

<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1552805" rel="1552805" data-issuekey="OEKB-6724" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6724" href="https://jira.vermeg.com/browse/OEKB-6724">OEKB-6724</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Major
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - TEND0000004716 - seev.031 - Offeror not mapped
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 17/Mar/25 3:05 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="17/Mar/25 3:29 PM"><time class="livestamp allow-future" datetime="2025-03-17T15:29:47+0000">17/Mar/25 3:29 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 25/Mar/25 3:23 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      TEND0000004716
<br/>
When checking seev.031 OUT (attached), I noticed that the Offeror (OFFO) is not populated although this info is available in the event data (see screenshot).
<br/>

<br/>
In the seev.031 OUT, only WEBB is available under &lt;AddtlInf&gt; =&gt;
<br/>

<br/>
&lt;AddtlInf&gt;
<br/>
&lt;URLAdr&gt;
<br/>
&lt;Lang&gt;en&lt;/Lang&gt;
<br/>
&lt;URLAdr&gt;[https://robau-beteiligung.at/|https://robau-beteiligung.at/]&lt;/URLAdr&gt;
<br/>
&lt;/URLAdr&gt;
<br/>
&lt;/AddtlInf&gt;
<br/>

<br/>
Could you please check?
<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1551229" rel="1551229" data-issuekey="OEKB-6719" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6719" href="https://jira.vermeg.com/browse/OEKB-6719">OEKB-6719</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - TEND0000004716 - UTC time in seev.031
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 14/Mar/25 11:09 AM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="14/Mar/25 11:18 AM"><time class="livestamp allow-future" datetime="2025-03-14T11:18:33+0000">14/Mar/25 11:18 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 25/Mar/25 3:23 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      TEND0000004716
<br/>
In the seev.031 OUT, deadlines are populated in the following format:
<br/>

<br/>
			&lt;DtTm&gt;2025-05-20T16:00:00.000Z&lt;/DtTm&gt;
<br/>

<br/>
As agreed during M12 testing, the requested format for ISO 20022 messages is
<br/>

<br/>
D*tTm incl. local time + main entity UTC indicator* 
<br/>
(i.e.: &lt;DtTm&gt;2025-05-20T17:00:00+01:00&lt;/DtTm&gt;)
<br/>

<br/>
Could you please check and make sure that all time fields are mapped in the same correct format?
<br/>

<br/>
Thanks, Bert
<br/>

  </td>
                    </tr>


                <tr id="issuerow1551205" rel="1551205" data-issuekey="OEKB-6717" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6717" href="https://jira.vermeg.com/browse/OEKB-6717">OEKB-6717</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 SCoRE: Std. 13 - no MT564 generated - too long narrative text
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            OEKB - Stefan RIBISCH
    </td>
                                            <td class="created"> 14/Mar/25 10:32 AM </td>
                                            <td class="fixVersions">                        SCORE Patch_03042025            </td>
                                            <td class="customfield_12663">            <span title="14/Mar/25 10:52 AM"><time class="livestamp allow-future" datetime="2025-03-14T10:52:41+0000">14/Mar/25 10:52 AM</time></span>
    </td>
                                            <td class="resolutiondate"> 03/Apr/25 6:51 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      I created a reversal request for event DVCA0000003884 with three clients which receive ISO 15022 message MT564.
<br/>

<br/>
According to SWIFT, the length of the narrative field is 6*35 characters (= 210) but in reversal request input screen you can fill in 255 characters (which is allowed for ISO 20022 seev.035 REVR).
<br/>

<br/>
As a consequence: The MT564 are not generated now.
<br/>

<br/>
Please find a solution that only 210 characters are possible in narrative field - IF that is the reason for the not-generated notifications.
<br/>

<br/>
Thanks,
<br/>
stefan
  </td>
                    </tr>


                <tr id="issuerow1550458" rel="1550458" data-issuekey="OEKB-6711" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6711" href="https://jira.vermeg.com/browse/OEKB-6711">OEKB-6711</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - TEND0000004716 - No seev.034 sent to client
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 13/Mar/25 2:30 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="13/Mar/25 2:45 PM"><time class="livestamp allow-future" datetime="2025-03-13T14:45:53+0000">13/Mar/25 2:45 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 13/Mar/25 3:02 PM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      TEND0000004716
<br/>
I created a client instruction.
<br/>
When checking the Instruction Status Advice, no message (seev.034) is available under “View Message”.
<br/>

<br/>
Can you please check?
<br/>
Thanks, Bert
<br/>

<br/>

  </td>
                    </tr>


                <tr id="issuerow1550411" rel="1550411" data-issuekey="OEKB-6710" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6710" href="https://jira.vermeg.com/browse/OEKB-6710">OEKB-6710</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 SCoRE - TEND0000004716 - Generation of seev.031 failed
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Closed&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is considered finished, the resolution is correct. Issues which are closed can be reopened.&lt;/span&gt;">Closed</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 13/Mar/25 1:39 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="13/Mar/25 1:41 PM"><time class="livestamp allow-future" datetime="2025-03-13T13:41:31+0000">13/Mar/25 1:41 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 14/Mar/25 8:57 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      TEND0000004716
<br/>
I tried to send seev.031 for client 229500.
<br/>

<br/>
However, the generation failed with error message =&gt;
<br/>
&quot;Cannot invoke &quot;\[B.clone()&quot; because &quot;content&quot; is null&quot;
<br/>

<br/>
Please check\!
<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1546067" rel="1546067" data-issuekey="OEKB-6693" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6693" href="https://jira.vermeg.com/browse/OEKB-6693">OEKB-6693</a>
</td>
                                            <td class="issuelinks">                </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 QAS - PROX0000004640 - seev.004 not available in MegaCor
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Fixed Delivered&lt;/span&gt;">Fixed Delivered</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 05/Mar/25 1:24 PM </td>
                                            <td class="fixVersions">    &nbsp;
</td>
                                            <td class="customfield_12663">            <span title="05/Mar/25 1:27 PM"><time class="livestamp allow-future" datetime="2025-03-05T13:27:01+0000">05/Mar/25 1:27 PM</time></span>
    </td>
                                            <td class="resolutiondate"> 10/Mar/25 10:40 AM </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      PROX0000004640
<br/>
I wanted to import seev.004, status in MegaBroker is “ValidFlow”, but the instruction is not available in MegaCor.
<br/>
FlowIn, FlowOut attached.
<br/>
Could you please check?
<br/>
Thanks, Bert
  </td>
                    </tr>


                <tr id="issuerow1545180" rel="1545180" data-issuekey="OEKB-6691" class="issuerow">
                                            <td class="issuetype">    Incident
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="OEKB-6691" href="https://jira.vermeg.com/browse/OEKB-6691">OEKB-6691</a>
</td>
                                            <td class="issuelinks">                                        VEGGO-7597                        </td>
                                            <td class="priority">           Medium
    </td>
                                            <td class="summary"><p>
                M12 QAS - SOFF0000004560 - Validation of update failed
    </p>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-success jira-issue-status-lozenge-done aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;" title="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Planned&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;This issue is ready to be handled.&lt;/span&gt;">Planned</span>    </td>
                                            <td class="assignee">            Emna EL MEKKI
    </td>
                                            <td class="creator">            Bertram Schon
    </td>
                                            <td class="created"> 03/Mar/25 2:39 PM </td>
                                            <td class="fixVersions">                        MR3_2025            </td>
                                            <td class="customfield_12663">            <span title="03/Mar/25 2:41 PM"><time class="livestamp allow-future" datetime="2025-03-03T14:41:34+0000">03/Mar/25 2:41 PM</time></span>
    </td>
                                            <td class="resolutiondate"> &nbsp; </td>
                                            <td class="customfield_12503">    NO
</td>
                                            <td class="customfield_20812"></td>
                                            <td class="description">      SOFF0000004560
<br/>
I set up a SOFF event by importing MT564 (attached). 
<br/>

<br/>
After saving, Ana tried to validate, but the below error popped up.
<br/>
Please check\!
<br/>
Thanks, Bert
  </td>
                    </tr>
                </tbody>
    </table>
    </issuetable-web-component>
    <div class="end-of-stable-message"></div>
            <table border="1" >
<tr>
    <td bgcolor="#dddddd" colspan="15"><font size="1">
        Generated at Tue May 20 10:46:13 UTC 2025 by Emna EL MEKKI using Jira 9.4.25#940025-sha1:6cf935f91739b43d1216a637e5c998e86376bd2e.
    </font></td>
</tr>
</table>

</body>
</html>